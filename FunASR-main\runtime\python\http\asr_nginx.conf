user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    upstream backend {
        # 最少连接算法
        least_conn;
        # 启动的服务地址
        server localhost:8001;
        server localhost:8002;
        server localhost:8003;
    }

    server {
        # 实际访问的端口
        listen 8000;

        location / {
            proxy_pass http://backend;
        }
    }

    include /etc/nginx/conf.d/*.conf;
}
if HAVE_COMPRESS
compress_include_headers = fst/extensions/compress/compress.h \
fst/extensions/compress/compress-script.h fst/extensions/compress/gzfile.h \
fst/extensions/compress/elias.h fst/extensions/compress/randmod.h
endif

if HAVE_FAR
far_include_headers = fst/extensions/far/compile-strings.h \
fst/extensions/far/create.h fst/extensions/far/equal.h \
fst/extensions/far/extract.h fst/extensions/far/far.h \
fst/extensions/far/far-class.h fst/extensions/far/farlib.h \
fst/extensions/far/farscript.h fst/extensions/far/getters.h \
fst/extensions/far/info.h fst/extensions/far/isomorphic.h \
fst/extensions/far/print-strings.h fst/extensions/far/script-impl.h \
fst/extensions/far/stlist.h fst/extensions/far/sttable.h
endif

if HAVE_LINEAR
linear_include_headers = fst/extensions/linear/linear-fst-data-builder.h \
fst/extensions/linear/linear-fst-data.h fst/extensions/linear/linear-fst.h \
fst/extensions/linear/linearscript.h fst/extensions/linear/loglinear-apply.h \
fst/extensions/linear/trie.h
endif

if HAVE_MPDT
mpdt_include_headers = fst/extensions/mpdt/compose.h \
fst/extensions/mpdt/expand.h fst/extensions/mpdt/info.h \
fst/extensions/mpdt/mpdt.h fst/extensions/mpdt/mpdtlib.h \
fst/extensions/mpdt/mpdtscript.h fst/extensions/mpdt/read_write_utils.h \
fst/extensions/mpdt/reverse.h
endif

if HAVE_NGRAM
ngram_include_headers = fst/extensions/ngram/bitmap-index.h \
fst/extensions/ngram/ngram-fst.h fst/extensions/ngram/nthbit.h
endif

if HAVE_PDT
pdt_include_headers = fst/extensions/pdt/collection.h \
fst/extensions/pdt/compose.h fst/extensions/pdt/expand.h \
fst/extensions/pdt/getters.h fst/extensions/pdt/info.h \
fst/extensions/pdt/paren.h fst/extensions/pdt/pdt.h \
fst/extensions/pdt/pdtlib.h fst/extensions/pdt/pdtscript.h \
fst/extensions/pdt/replace.h fst/extensions/pdt/reverse.h \
fst/extensions/pdt/shortest-path.h
endif

if HAVE_SPECIAL
special_include_headers = fst/extensions/special/phi-fst.h \
fst/extensions/special/rho-fst.h fst/extensions/special/sigma-fst.h
endif

if HAVE_GRM
far_include_headers = fst/extensions/far/compile-strings.h \
fst/extensions/far/create.h fst/extensions/far/equal.h \
fst/extensions/far/extract.h fst/extensions/far/far.h \
fst/extensions/far/far-class.h fst/extensions/far/farlib.h \
fst/extensions/far/farscript.h fst/extensions/far/getters.h \
fst/extensions/far/info.h fst/extensions/far/isomorphic.h \
fst/extensions/far/print-strings.h fst/extensions/far/script-impl.h \
fst/extensions/far/stlist.h fst/extensions/far/sttable.h
mpdt_include_headers = fst/extensions/mpdt/compose.h \
fst/extensions/mpdt/expand.h fst/extensions/mpdt/info.h \
fst/extensions/mpdt/mpdt.h fst/extensions/mpdt/mpdtlib.h \
fst/extensions/mpdt/mpdtscript.h fst/extensions/mpdt/read_write_utils.h \
fst/extensions/mpdt/reverse.h
pdt_include_headers = fst/extensions/pdt/collection.h \
fst/extensions/pdt/compose.h fst/extensions/pdt/expand.h \
fst/extensions/pdt/getters.h fst/extensions/pdt/info.h \
fst/extensions/pdt/paren.h fst/extensions/pdt/pdt.h \
fst/extensions/pdt/pdtlib.h fst/extensions/pdt/pdtscript.h \
fst/extensions/pdt/replace.h fst/extensions/pdt/reverse.h \
fst/extensions/pdt/shortest-path.h
endif

script_include_headers = fst/script/arc-class.h \
fst/script/arciterator-class.h fst/script/arcsort.h \
fst/script/arg-packs.h fst/script/closure.h fst/script/compile-impl.h \
fst/script/compile.h fst/script/compose.h fst/script/concat.h \
fst/script/connect.h fst/script/convert.h fst/script/decode.h \
fst/script/determinize.h fst/script/difference.h fst/script/disambiguate.h \
fst/script/draw-impl.h fst/script/draw.h fst/script/encode.h \
fst/script/encodemapper-class.h fst/script/epsnormalize.h fst/script/equal.h \
fst/script/equivalent.h fst/script/fst-class.h fst/script/fstscript.h \
fst/script/getters.h fst/script/info-impl.h fst/script/info.h \
fst/script/intersect.h fst/script/invert.h fst/script/isomorphic.h \
fst/script/map.h fst/script/minimize.h fst/script/print-impl.h \
fst/script/print.h fst/script/project.h fst/script/prune.h \
fst/script/push.h fst/script/randequivalent.h fst/script/randgen.h \
fst/script/register.h fst/script/relabel.h fst/script/replace.h \
fst/script/reverse.h fst/script/reweight.h fst/script/rmepsilon.h \
fst/script/script-impl.h fst/script/shortest-distance.h \
fst/script/shortest-path.h fst/script/stateiterator-class.h \
fst/script/synchronize.h fst/script/text-io.h fst/script/topsort.h \
fst/script/union.h fst/script/weight-class.h fst/script/fstscript-decl.h \
fst/script/verify.h

test_include_headers = fst/test/algo_test.h fst/test/fst_test.h \
fst/test/rand-fst.h fst/test/weight-tester.h

nobase_include_HEADERS = fst/accumulator.h fst/add-on.h fst/arc-arena.h \
fst/arc-map.h fst/arc.h fst/arcfilter.h fst/arcsort.h fst/bi-table.h \
fst/cache.h fst/closure.h fst/compact-fst.h fst/compat.h fst/complement.h \
fst/compose-filter.h fst/compose.h fst/concat.h fst/config.h fst/connect.h \
fst/const-fst.h fst/determinize.h fst/dfs-visit.h fst/difference.h \
fst/disambiguate.h fst/edit-fst.h fst/encode.h fst/epsnormalize.h fst/equal.h \
fst/equivalent.h fst/expanded-fst.h fst/expectation-weight.h \
fst/factor-weight.h fst/filter-state.h fst/flags.h fst/float-weight.h \
fst/fst-decl.h fst/fst.h fst/fstlib.h fst/generic-register.h fst/heap.h \
fst/icu.h fst/intersect.h fst/interval-set.h fst/invert.h fst/isomorphic.h \
fst/label-reachable.h fst/lexicographic-weight.h fst/lock.h fst/log.h \
fst/lookahead-filter.h fst/lookahead-matcher.h fst/map.h fst/mapped-file.h \
fst/matcher-fst.h fst/matcher.h fst/memory.h fst/minimize.h fst/mutable-fst.h \
fst/pair-weight.h fst/partition.h fst/power-weight.h fst/product-weight.h \
fst/project.h fst/properties.h fst/prune.h fst/push.h fst/queue.h \
fst/randequivalent.h fst/randgen.h fst/rational.h fst/register.h \
fst/relabel.h fst/replace-util.h fst/replace.h fst/reverse.h fst/reweight.h \
fst/rmepsilon.h fst/rmfinalepsilon.h fst/set-weight.h fst/shortest-distance.h \
fst/shortest-path.h fst/signed-log-weight.h fst/sparse-power-weight.h \
fst/sparse-tuple-weight.h fst/state-map.h fst/state-reachable.h \
fst/state-table.h fst/statesort.h fst/string-weight.h fst/string.h \
fst/symbol-table-ops.h fst/symbol-table.h fst/synchronize.h \
fst/test-properties.h fst/topsort.h fst/tuple-weight.h fst/types.h \
fst/union-find.h fst/union-weight.h fst/union.h fst/util.h fst/vector-fst.h \
fst/verify.h fst/visit.h fst/weight.h \
$(compress_include_headers) \
$(far_include_headers) \
$(linear_include_headers) \
$(mpdt_include_headers) \
$(ngram_include_headers) \
$(pdt_include_headers) \
$(script_include_headers) \
$(special_include_headers) \
$(test_include_headers)

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
<!--
    User settings, to simplify setup. The names and values of make variables
    used by MsBuild scripts are not obvious, so I thought it would be easier
    if the list of user choices in available in a single file.

    All settings are disabled by default. Enable any of them by removing the
    'Condition="false"' part.

    You can instead specify these values in MSBuild command line. For example,
    you can put the following command into a build.cmd file:

    msbuild openfst.sln -v:m -m -t:Build ^
              -p:Platform=x64 ^
              -p:Configuration=Release ^
              -p:PlatformToolset=v141 ^
              -p:WindowsTargetPlatformVersion=10.0.16299.0 ^
              -p:EnableEnhancedInstructionSet=AdvancedVectorExtensions
-->

  <!-- Toolset (default v141). Other compilers (Intel C++) may be specified. -->
  <PropertyGroup>
    <!-- v140 = 14.0, comes with VS14 aka VS 2015 -->
    <PlatformToolset Condition="false" >v140</PlatformToolset>
    <!-- v141 = 14.1, comes with VS15 aka VS 2017. This is the default. -->
    <PlatformToolset Condition="false" >v141</PlatformToolset>
  </PropertyGroup>

  <!-- SDK version (default: toolset's default, likely 8.1). Note that v141 has
       8.1 as the default SDK, but that SDK may not be installed with VS 2017
       default C++ workload. If you get a message "MSB8036: The Windows SDK
       version 8.1 was not found", check if you have any of Window 10 SDK
       versions. Then change 'false' to 'true' for the version you have installed.

       Run "x{86,64} Native Tools Command Prompt for VS 201{5,7}" from the
       Start Menu, and type "set WindowsSDKVersion". The value of the
       environment variable shows the installed version of the Windows 10 SDK.

       You can also look for the versions in subdirectories of the folder
       %ProgramFiles(x86)%\Windows Kits\10\DesignTime\CommonConfiguration\Neutral\UAP
       (assuming the default install location for Windows SDK). Every subdirectory
       name is the Windows SDK version that you can use.

       I am including a (possibly incomplete) list of released Windows 10 SDK
       versions (any one would work, as long as you have it installed). -->
  <PropertyGroup>
    <!-- Windows 10 Update 1803 (April 2018) -->
    <WindowsTargetPlatformVersion Condition="false" >10.0.17134.0</WindowsTargetPlatformVersion>
    <!-- Windows 10 Fall Creators Update  -->
    <WindowsTargetPlatformVersion Condition="false" >10.0.16299.0</WindowsTargetPlatformVersion>
    <!-- Windows 10 Creators Update -->
    <WindowsTargetPlatformVersion Condition="false" >10.0.15063.0</WindowsTargetPlatformVersion>
    <!-- Windows 10 Anniversary Edition -->
    <WindowsTargetPlatformVersion Condition="false" >10.0.14393.0</WindowsTargetPlatformVersion>
    <!-- Windows 10 November 2015 SDK update-->
    <WindowsTargetPlatformVersion Condition="false" >10.0.10586.0</WindowsTargetPlatformVersion>
    <!-- Windows 10 original SDK, can have either of the two versions below:  -->
    <WindowsTargetPlatformVersion Condition="false" >10.0.10240.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformVersion Condition="false" >10.0.26624.0</WindowsTargetPlatformVersion>
  </PropertyGroup>

  <!-- Enhanced instruction set (default SSE2, I believe).
       NOTE: In kkm's experience, AVX helps the performance a little
       (few % at most), AVX2 does not improve performance further. YMMV:
       Maybe it's our FST structure that does not yield to optimization.

       Currently, there is no MS c2 codegen for AVX512 instructions.

       Note: cl 32-bit compiler likely does not support AVX/AVX2. -->
  <PropertyGroup>
    <!-- AVX -->
    <EnableEnhancedInstructionSet Condition="false" >AdvancedVectorExtensions</EnableEnhancedInstructionSet>

    <!-- AVX2 -->
    <EnableEnhancedInstructionSet Condition="false" >AdvancedVectorExtensions2</EnableEnhancedInstructionSet>
  </PropertyGroup>

</Project>

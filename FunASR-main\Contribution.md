# Contributing to FunASR

First off, thanks for taking the time to contribute! 🎉

The following is a set of guidelines for contributing to FunASR. These are mostly guidelines, not rules. Use your best judgment, and feel free to propose changes to this document in a pull request.

## How Can I Contribute?

### Reporting Bugs

This section guides you through submitting a bug report for FunASR. Following these guidelines helps maintainers and the community understand your report, reproduce the behavior, and find related reports.

- **Ensure the bug was not already reported** by searching on GitHub under Issues.
- If you're unable to find an open issue addressing the problem, open a new one. Be sure to include a **title and clear description**, as much relevant information as possible, and a **code sample** or an **executable test case** demonstrating the expected behavior that is not occurring.

### Suggesting Enhancements

This section guides you through submitting an enhancement suggestion for FunASR, including completely new features and minor improvements to existing functionality.

- **Ensure the enhancement was not already suggested** by searching on GitHub under Issues.
- If you find an enhancement that matches your suggestion, feel free to add your comments to the existing issue.
- If you don't find an existing issue, you can open a new one. Be sure to include a **title and clear description**, as much relevant information as possible, and a **code sample** or an **executable test case** demonstrating the expected behavior.

### Pull Requests

The process described here has several goals:

- Maintain FunASR's quality
- Fix problems that are important to users
- Engage the community in working toward the best possible FunASR

Please follow these steps to have your contribution considered by the maintainers:

1. **Fork** the repository.
2. **Clone** your fork: `git clone https://github.com/alibaba/FunASR.git`
3. **Create a branch** for your changes: `git checkout -b my-new-feature`
4. **Make your changes**.
5. **Commit your changes**: `git commit -am 'Add some feature'`
6. **Push to the branch**: `git push origin my-new-feature`
7. **Create a new Pull Request**.

### Code of Conduct

This project and everyone participating in it is governed by the FunASR Code of Conduct. By participating, you are expected to uphold this code.

Thank you for contributing to FunASR!

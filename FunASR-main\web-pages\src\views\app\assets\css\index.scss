html,
body {
    width: 100%;
    height: 100%;
}
html > body {
    overflow: hidden;
    min-width: 1280px;
    max-width: 1920px;
    margin: 0 auto;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

p {
    padding: 0;
    margin: 0;
}

ul,
li {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
}

a {
    text-decoration: none;
}

input,
button,
textarea {
    outline: none;
    border: none;
}

textarea {
    resize: none;
}

img {
    border: none;
    vertical-align: middle;
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.xmgl-icon {
    line-height: 1;
}

.app-container {
    width: 100%;
    height: 100%;
    background-color: #eff2f6;
    .app-content{
        width: 100%;
        height: 100%;
    }
}
.page-container {
    width: 100%;
    padding: p(15);

    .page-title {
        padding-top: p(45);
        padding-bottom: p(35);
        font-size: p(40);
        font-weight: 700;
        color: #333333;
        text-align: center;
    }
}
.page-content {
    width: p(1200);
    background-color: #ffffff;
    margin: 0 auto;
    box-shadow: 0 1px 0 0 rgba(178, 188, 198, 0.51);
}
.page-list-content {
    background-color: #ffffff;
    box-shadow: 0 1px 0 0 rgba(178, 188, 198, 0.51);
}

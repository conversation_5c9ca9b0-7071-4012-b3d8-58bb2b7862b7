"""
音频转录模块
使用OpenAI Whisper进行音频转文字
"""

import os
import whisper
import torch
from pathlib import Path
from datetime import datetime
import logging
from tqdm import tqdm
import subprocess
import sys
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BaseTranscriber(ABC):
    @abstractmethod
    def transcribe(self, audio_path, language=None, output_path=None):
        pass

    @abstractmethod
    def get_model_info(self):
        pass

class WhisperTranscriber(BaseTranscriber):
    """音频转录器类"""
    
    def __init__(self, model_size="base", txt_dir="txt", device=None):
        """
        初始化转录器

        Args:
            model_size: Whisper模型大小 (tiny, base, small, medium, large)
            txt_dir: 文本文件保存目录
            device: 计算设备 (cuda/cpu)，None为自动选择
        """
        self.txt_dir = Path(txt_dir)
        self.txt_dir.mkdir(exist_ok=True)

        # 设置FFmpeg环境
        self._setup_ffmpeg()

        # 自动选择设备
        if device is None:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        logger.info(f"使用设备: {self.device}")

        # 加载Whisper模型
        self.model_size = model_size
        self.model = None
        self._load_model()

    def _setup_ffmpeg(self):
        """设置FFmpeg环境变量"""
        try:
            # 检查本地FFmpeg
            local_ffmpeg_dir = Path("ffmpeg/bin").absolute()
            if local_ffmpeg_dir.exists():
                # 将FFmpeg目录添加到PATH环境变量
                current_path = os.environ.get('PATH', '')
                ffmpeg_path_str = str(local_ffmpeg_dir)

                if ffmpeg_path_str not in current_path:
                    os.environ['PATH'] = ffmpeg_path_str + os.pathsep + current_path
                    logger.info(f"已将FFmpeg路径添加到环境变量: {ffmpeg_path_str}")

                # 验证FFmpeg是否可用
                try:
                    result = subprocess.run(
                        ['ffmpeg', '-version'],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )
                    if result.returncode == 0:
                        logger.info("FFmpeg环境设置成功")
                    else:
                        logger.warning("FFmpeg验证失败")
                except Exception as e:
                    logger.warning(f"FFmpeg验证异常: {e}")
            else:
                logger.warning("本地FFmpeg目录不存在")
        except Exception as e:
            logger.error(f"设置FFmpeg环境失败: {e}")

    def _load_model(self):
        """加载Whisper模型"""
        try:
            logger.info(f"正在加载Whisper模型: {self.model_size}")
            self.model = whisper.load_model(self.model_size, device=self.device)
            logger.info("模型加载成功")
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def get_available_models(self):
        """获取可用的模型列表"""
        return whisper.available_models()
    
    def transcribe(self, audio_path, language=None, output_path=None):
        """
        转录音频文件
        
        Args:
            audio_path: 音频文件路径
            language: 指定语言代码 (zh, en等)，None为自动检测
            output_path: 输出文件路径，None为自动生成
            
        Returns:
            tuple: (是否成功, 转录文本文件路径或错误信息)
        """
        try:
            audio_path = Path(audio_path)
            if not audio_path.exists():
                return False, f"音频文件不存在: {audio_path}"
            
            logger.info(f"开始转录音频: {audio_path.name}")
            
            # 转录音频
            result = self.model.transcribe(
                str(audio_path),
                language=language,
                verbose=True
            )
            
            # 生成输出文件路径
            if output_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_name = audio_path.stem
                output_filename = f"{base_name}_transcript_{timestamp}.txt"
                output_path = self.txt_dir / output_filename
            else:
                output_path = Path(output_path)
            
            # 保存转录结果
            self._save_transcript(result, output_path)
            
            logger.info(f"转录完成: {output_path}")
            return True, str(output_path)
            
        except Exception as e:
            logger.error(f"转录失败: {e}")
            return False, str(e)
    
    def _save_transcript(self, result, output_path):
        """
        保存转录结果到文件
        
        Args:
            result: Whisper转录结果
            output_path: 输出文件路径
        """
        with open(output_path, 'w', encoding='utf-8') as f:
            # 写入基本信息
            f.write("=" * 50 + "\n")
            f.write("音频转录结果\n")
            f.write(f"转录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"检测语言: {result.get('language', '未知')}\n")
            f.write("=" * 50 + "\n\n")
            
            # 写入完整转录文本（带标点）
            f.write("完整转录文本（含标点）：\n")
            f.write("-" * 30 + "\n")
            f.write(result['text'].strip() + "\n\n")
            
            # 写入分段文本（带时间戳）
            f.write("分段转录文本：\n")
            f.write("-" * 30 + "\n")
            
            for segment in result['segments']:
                start_time = self._format_time(segment['start'])
                end_time = self._format_time(segment['end'])
                text = segment['text'].strip()
                f.write(f"[{start_time} - {end_time}] {text}\n")
    
    def _format_time(self, seconds):
        """
        格式化时间戳
        
        Args:
            seconds: 秒数
            
        Returns:
            格式化的时间字符串 (MM:SS)
        """
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
    
    def batch_transcribe(self, audio_dir, language=None):
        """
        批量转录音频文件
        
        Args:
            audio_dir: 音频文件目录
            language: 指定语言代码
            
        Returns:
            list: 转录结果列表 [(文件名, 是否成功, 结果路径或错误信息)]
        """
        audio_dir = Path(audio_dir)
        if not audio_dir.exists():
            logger.error(f"音频目录不存在: {audio_dir}")
            return []
        
        # 支持的音频格式
        audio_extensions = {'.mp3', '.wav', '.m4a', '.flac', '.ogg', '.wma'}
        audio_files = [
            f for f in audio_dir.iterdir() 
            if f.is_file() and f.suffix.lower() in audio_extensions
        ]
        
        if not audio_files:
            logger.warning(f"在目录 {audio_dir} 中未找到音频文件")
            return []
        
        results = []
        logger.info(f"找到 {len(audio_files)} 个音频文件，开始批量转录")
        
        for audio_file in tqdm(audio_files, desc="转录进度"):
            success, result = self.transcribe(audio_file, language)
            results.append((audio_file.name, success, result))
            
            if success:
                logger.info(f"✓ {audio_file.name} 转录成功")
            else:
                logger.error(f"✗ {audio_file.name} 转录失败: {result}")
        
        return results
    
    def get_model_info(self):
        """获取当前模型信息"""
        if self.model is None:
            return "模型未加载"
        
        return {
            "model_size": self.model_size,
            "device": self.device,
            "available_models": self.get_available_models()
        }

class FunASRTranscriber(BaseTranscriber):
    def __init__(self, model_name="paraformer-zh", txt_dir="txt", device=None):
        self.txt_dir = Path(txt_dir)
        self.txt_dir.mkdir(exist_ok=True)
        self.model_name = model_name
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        try:
            from funasr import AutoModel
        except ImportError:
            print("⚠️  FunASR库未安装，正在尝试自动安装...")
            try:
                import subprocess
                import sys
                # 尝试安装FunASR
                subprocess.check_call([sys.executable, "-m", "pip", "install", "funasr", "modelscope"])
                from funasr import AutoModel
                print("✅ FunASR安装成功！")
            except Exception as e:
                raise ImportError(f"FunASR安装失败。请手动安装：pip install funasr modelscope\n错误详情: {e}")
        self.AutoModel = AutoModel
        self.model = self.AutoModel(model=self.model_name, device=self.device)

    def transcribe(self, audio_path, language=None, output_path=None):
        try:
            audio_path = Path(audio_path)
            if not audio_path.exists():
                return False, f"音频文件不存在: {audio_path}"
            result = self.model.generate(input=str(audio_path), language=language)
            text = result[0]["text"] if result and "text" in result[0] else ""
            if output_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_name = audio_path.stem
                output_filename = f"{base_name}_funasr_transcript_{timestamp}.txt"
                output_path = self.txt_dir / output_filename
            else:
                output_path = Path(output_path)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text)
            return True, str(output_path)
        except Exception as e:
            logging.error(f"FunASR转录失败: {e}")
            return False, str(e)

    def get_model_info(self):
        return {
            "model_name": self.model_name,
            "device": self.device
        }

def get_transcriber(model_type, **kwargs):
    if model_type == "whisper":
        return WhisperTranscriber(**kwargs)
    elif model_type == "funasr":
        return FunASRTranscriber(**kwargs)
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

def main():
    """测试函数"""
    print("音频转录器测试")
    print("请选择模型类型: whisper / funasr")
    model_type = input("模型类型 [whisper]: ").strip() or "whisper"
    if model_type == "whisper":
        model_size = input("Whisper模型大小 (tiny/base/small/medium/large) [base]: ").strip() or "base"
        transcriber = get_transcriber("whisper", model_size=model_size)
    elif model_type == "funasr":
        model_name = input("FunASR模型名 [paraformer-zh]: ").strip() or "paraformer-zh"
        transcriber = get_transcriber("funasr", model_name=model_name)
    else:
        print("不支持的模型类型")
        return
    print(f"模型信息: {transcriber.get_model_info()}")
    audio_path = input("请输入音频文件路径: ").strip()
    if audio_path and Path(audio_path).exists():
        print("开始转录...")
        success, result = transcriber.transcribe(audio_path)
        if success:
            print(f"转录成功: {result}")
        else:
            print(f"转录失败: {result}")
    else:
        print("音频文件不存在或路径为空")

if __name__ == "__main__":
    main()

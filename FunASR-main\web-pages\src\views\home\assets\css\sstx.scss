.sstx-comp{
    width: 100%;
    height: p(1100);
    background: url('../images/sstx-bg.png') center no-repeat;
    background-size: 100% 100%;

    .lxwjzxfw-plan{
        padding: p(0) p(350);
        display: flex;
        flex-direction: column;
    }
    .sstx-plan{
        // padding: p(0) p(350);
        display: flex;
        flex-direction: column;
    }
    h3 {
        color: #fff;
        font-size: p(22);
        position: relative;
        line-height: p(46);
        div {
             position: absolute;
             bottom: 0;
             left: 0;
             width: p(45);
             height: 2px;
             background-color: #aeaeb7;
        }
   }

   .sstx-lc{
        padding-top: p(36);
        .box{
            flex: 1;
            display: flex;
            justify-content: center;
            position: relative;
            .lc-plan{
                width: p(250);
                height: p(84);
                background-image: linear-gradient(140deg, 
                    #e0e4f0 0%, 
                    #b3bad1 100%);
                border-radius: 6px;

                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                p:nth-of-type(1) {
                    font-size: p(20);
                    color: #000000;
                    font-weight: 600;
                }
                p:nth-of-type(2) {
                    font-size: p(16);
                    color: #515c73;
                }
            }
            .icon-plan{
                
            }
        }
        .box-min{
            .lc-plan{
                width: p(180);
                height: p(70);
            }
        }
        .blue-box{
            .lc-plan{
                background-image: linear-gradient(140deg, 
                    rgba(22, 92, 255, 0.3) 0%, 
                    rgba(22, 92, 255, 0.3) 100%);
                border-radius: 6px;
                border: dashed 2px rgba(22, 92, 255, 0.8);

                p{
                    color: #fff !important;
                }
                p:nth-of-type(1) {
                    font-size: p(20);
                }
                p:nth-of-type(2) {
                    font-size: p(16);
                }
            }
        }
        .lc-top{
            display: flex;
            .lc-plan{

            }
            .icon-plan{
                
            }
            .box-1{
                .icon-plan{
                    position: absolute;
                    bottom: p(-48);
                    img{
                        width: p(18);
                    }
                    p{
                        position: absolute;
                        font-size: p(18);
                        color: #fff;
                        top: 10%;
                        right: 50%;
                        transform: translate(60%, 0);
                        width: max-content;
                        letter-spacing: p(15);
                    }
                }
            }
        }
        .lc-center{
            display: flex;
            align-items: center;
            height: p(115);
            background-color: rgba(179, 186, 209, 0.25);
            border-radius: 4px;
            border: solid 1px rgba(179, 186, 209, 0.8);
            margin-top: p(30);
            margin-bottom: p(20);
            .box-1,.box-2{
                
                .icon-plan,.icon-plan1{
                    position: absolute;
                    // bottom: p(-48);
                    right: p(-60);
                    top: 50%;
                    transform: translate(0, -50%);
                    img{
                        width: p(140);
                        height: p(18);
                    }
                    div{
                        position: absolute;
                        top: p(-12);
                        width: p(140);
                        p{
                            font-size: p(18);
                            color: #fff;
                            text-align: center;
                        }
                    }
                }
            }
            .box-2{
                .icon-plan1{
                    right: p(-200);
                    img{
                        width: p(286);
                    }
                    div{
                        width: p(286);
                    }
                }
                .icon-plan2{
                    position: absolute;
                    bottom: p(-80);
                    img{
                        width: p(18);
                        height: p(90);
                    }
                    p{
                        position: absolute;
                        font-size: p(18);
                        color: #fff;
                        top: 60%;
                        right: 50%;
                        transform: translate(50%, 0);
                        width: max-content;
                    }
                }
            }
        }
        .lc-bottom{
            display: flex;
            align-items: center;
            padding-top: p(20);
            height: p(160);
            background-color: rgba(179, 186, 209, 0.25);
            border-radius: 4px;
            border: solid 1px rgba(179, 186, 209, 0.8);
            .box-1{
                .icon-plan1{
                    position: absolute;
                    top: p(-90);
                    img{
                        width: p(18);
                        height: p(90);
                    }
                    p{
                        position: absolute;
                        font-size: p(18);
                        color: #fff;
                        top: 60%;
                        right: 50%;
                        transform: translate(50%, 0);
                        width: max-content;
                    }
                }
                .icon-plan2{
                    position: absolute;
                    right: p(-65);
                    top: 50%;
                    transform: translate(0, -50%);
                    img{
                        width: p(140);
                        height: p(18);
                    }
                    div{
                        position: absolute;
                        top: p(-12);
                        width: p(140);
                        p{
                            font-size: p(18);
                            color: #fff;
                            text-align: center;
                        }
                    }
                }
            }

            .box-2{
                .icon-plan{
                    position: absolute;
                    right: p(-30);
                    top: 50%;
                    transform: translate(0, -50%);
                    img{
                        width: p(140);
                        height: p(18);
                    }
                }
            }

            .box-3{
                .icon-plan{
                    position: absolute;
                    top: p(-210);
                    img{
                        width: p(18);
                        height: p(210);
                    }
                    p{
                        position: absolute;
                        font-size: p(18);
                        color: #fff;
                        bottom: 2%;
                        right: 50%;
                        transform: translate(65%, 0);
                        width: max-content;
                    }
                }
            }
        }

        .lxwj-text{
            color: #ffffff;
            font-size: p(18);
            margin-top: p(45);
        }
   }

   .xq-box{
    display: flex;
    align-items: flex-end;
    .video-box{
        width: p(517);
        height: p(290);
        // background-color: #165dff;
        margin-right: p(188);
        iframe{
            width: 100%;
            height: 100%;
        }
    }

    .btn-box{
        margin-top: p(80);
        li{
            width: p(190);
            height: p(55);
            background-color: #165dff;
            border-radius: p(4);
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            margin-bottom: p(40);
            cursor: pointer;
            img{
                width: p(26);
            }
            p{
                color: #ffffff;
                font-size: p(24);
            }

        }
        li:last-child {
            margin-bottom: p(15);
        }
        li:hover{
            background-color: #3f77fc;
        }
    }
}
}
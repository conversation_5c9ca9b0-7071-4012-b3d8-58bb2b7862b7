.ui-scrollbar {
    overflow: hidden;
    position: relative;

    &.child-over-width {
        .ui-scrollbar__view {
            max-width: 100%;
        }
    }

    &:active > .ui-scrollbar__bar,
    &:focus > .ui-scrollbar__bar,
    &:hover > .ui-scrollbar__bar {
        opacity: 1;
        transition: opacity 0.34s ease-out;
    }
}

.ui-scrollbar__wrap {
    overflow: scroll;
    height: 100%;
}
.ui-scrollbar__view {
    display: inline-block;
    vertical-align: middle;
    min-width: 100%;
}

.ui-scrollbar__bar {
    position: absolute;
    right: 2px;
    bottom: 2px;
    z-index: 1;
    border-radius: 4px;
    opacity: 0;
    -webkit-transition: opacity 120ms ease-out;
    transition: opacity 120ms ease-out;

    &.is-horizontal {
        height: 6px;
        left: 2px;

        & > div {
            height: 100%;
        }
    }

    &.is-vertical {
        width: 6px;
        top: 2px;

        & > div {
            width: 100%;
        }
    }
}

.ui-scrollbar__thumb {
    position: relative;
    display: block;
    width: 0;
    height: 0;
    cursor: pointer;
    border-radius: inherit;
    background-color: rgba(144, 147, 153, 0.3);
    -webkit-transition: 0.3s background-color;
    transition: 0.3s background-color;

    &:hover {
        background-color: hsla(220, 4%, 58%, 0.5);
    }
}

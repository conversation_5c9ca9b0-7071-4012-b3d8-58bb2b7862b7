# Copyright (c) 2021, NVIDIA CORPORATION.  All rights reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Created on 2024-01-01
# Author: GuAn Zhu

name: "encoder"
backend: "onnxruntime"
default_model_filename: "model.onnx"

max_batch_size: 128


sequence_batching{
    max_sequence_idle_microseconds: 15000000
    oldest {
      max_candidate_sequences: 1024
      preferred_batch_size: [32, 64, 128]
      max_queue_delay_microseconds: 300
    }
    control_input [
    ]
    state [
  ]
}


input [
  {
    name: "speech"
    data_type: TYPE_FP32
    dims: [-1, 560]
  },
  {
    name: "speech_lengths"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  }
]

output [
  {
    name: "enc"
    data_type: TYPE_FP32
    dims: [-1, 512]
  },
  {
    name: "enc_len"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  },
  {
    name: "alphas"
    data_type: TYPE_FP32
    dims: [-1]
  }
]

parameters { key: "cudnn_conv_algo_search" value: { string_value: "2" } }

instance_group [
    {
      count: 1
      kind: KIND_GPU
    }
]

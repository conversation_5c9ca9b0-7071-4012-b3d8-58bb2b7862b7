// Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
// Reserved. MIT License  (https://opensource.org/licenses/MIT)
//
// 2023 by bur<PERSON><PERSON><PERSON>(刘柏基) l<PERSON><PERSON><PERSON>@xverse.cn

syntax = "proto3";

option objc_class_prefix = "paraformer";

package paraformer;

service ASR {
  rpc Recognize (stream Request) returns (stream Response) {}
}

enum WavFormat {
  pcm = 0;
}

enum DecodeMode {
  offline = 0;
  online = 1;
  two_pass = 2;
}

message Request {
  DecodeMode mode = 1;
  WavFormat wav_format = 2;
  int32 sampling_rate = 3;
  repeated int32 chunk_size = 4;
  bool is_final = 5;
  bytes audio_data = 6;
}

message Response {
  DecodeMode mode = 1;
  string text = 2;
  bool is_final = 3;
}

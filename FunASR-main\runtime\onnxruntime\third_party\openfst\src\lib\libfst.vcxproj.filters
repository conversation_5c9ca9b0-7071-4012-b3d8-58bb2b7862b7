﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="compat.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="flags.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fst.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="properties.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="symbol-table.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="symbol-table-ops.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="util.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="weight.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mapped-file.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fst-types.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\include\fst\accumulator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\add-on.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\arc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\arc-arena.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\arcfilter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\arc-map.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\arcsort.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\bi-table.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\cache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\closure.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\compact-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\compat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\complement.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\compose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\compose-filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\concat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\connect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\const-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\determinize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\dfs-visit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\difference.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\disambiguate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\edit-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\encode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\epsnormalize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\equal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\equivalent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\expanded-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\expectation-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\factor-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\filter-state.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\flags.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\float-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\fst-decl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\fstlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\generic-register.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\heap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\icu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\intersect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\interval-set.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\invert.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\isomorphic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\label-reachable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\lexicographic-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\lock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\log.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\lookahead-filter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\lookahead-matcher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\map.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\mapped-file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\matcher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\matcher-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\memory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\minimize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\mutable-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\pair-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\partition.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\power-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\product-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\project.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\properties.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\prune.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\push.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\randequivalent.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\randgen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\rational.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\register.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\relabel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\replace.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\replace-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\reverse.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\reweight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\rmepsilon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\rmfinalepsilon.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\shortest-distance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\shortest-path.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\signed-log-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\sparse-power-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\sparse-tuple-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\state-map.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\state-reachable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\statesort.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\state-table.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\string.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\string-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\symbol-table.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\symbol-table-ops.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\synchronize.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\test-properties.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\topsort.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\tuple-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\union.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\union-find.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\union-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\vector-fst.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\verify.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\visit.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\include\fst\set-weight.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
# Copyright (c) 2021, NVIDIA CORPORATION.  All rights reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Created on 2024-01-01
# Author: GuAn Zhu

name: "decoder"
backend: "onnxruntime"
default_model_filename: "decoder.onnx"

max_batch_size: 128

sequence_batching{
    max_sequence_idle_microseconds: 15000000
    oldest {
      max_candidate_sequences: 1024
      preferred_batch_size: [16, 32, 64]
    }
    control_input [
    ]
    state [
    {
      input_name: "in_cache_0"
      output_name: "out_cache_0"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_1"
      output_name: "out_cache_1"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_2"
      output_name: "out_cache_2"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_3"
      output_name: "out_cache_3"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_4"
      output_name: "out_cache_4"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_5"
      output_name: "out_cache_5"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_6"
      output_name: "out_cache_6"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_7"
      output_name: "out_cache_7"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_8"
      output_name: "out_cache_8"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_9"
      output_name: "out_cache_9"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_10"
      output_name: "out_cache_10"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_11"
      output_name: "out_cache_11"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_12"
      output_name: "out_cache_12"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_13"
      output_name: "out_cache_13"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_14"
      output_name: "out_cache_14"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "in_cache_15"
      output_name: "out_cache_15"
      data_type: TYPE_FP32
      dims: [ 512, 10 ]
      initial_state: {
       data_type: TYPE_FP32
       dims: [ 512, 10]
       zero_data: true
       name: "initial state"
      }
    }
  ]
}


input [
  {
    name: "enc"
    data_type: TYPE_FP32
    dims: [-1, 512]
  },
  {
    name: "enc_len"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  },
  {
    name: "acoustic_embeds"
    data_type: TYPE_FP32
    dims: [-1, 512]
  },
  {
    name: "acoustic_embeds_len"
    data_type: TYPE_INT32
    dims: [1]
    reshape: { shape: [ ] }
  }
]

output [
  {
    name: "logits"
    data_type: TYPE_FP32
    dims: [-1, 8404]
  },
  {
    name: "sample_ids"
    data_type: TYPE_INT64
    dims: [-1]
  }
]


instance_group [
    {
      count: 1
      kind: KIND_GPU
    }
]


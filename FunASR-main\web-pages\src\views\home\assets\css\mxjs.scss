.mxjs-comp {
     background: url('../images/mxjs-bg.png') center no-repeat;
     background-size: 100% 100%;
     .mxjs-plan {
          padding: p(0) p(350);
          display: flex;
          flex-direction: column;
     }
     h3 {
          color: #24242f;
          font-size: p(22);
          position: relative;
          line-height: p(46);
          div {
               position: absolute;
               bottom: 0;
               left: 0;
               width: p(45);
               height: 2px;
               background-color: #aeaeb7;
          }
     }
     img {
          width: p(888);
          height: p(613);
          margin: auto;
     }
     p {
          color: #333333;
          font-size: p(18);
          margin-top: p(10);
          padding-bottom: p(83);
     }
}

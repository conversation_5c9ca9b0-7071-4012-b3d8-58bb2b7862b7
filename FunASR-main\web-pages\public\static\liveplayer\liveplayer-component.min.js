!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("LivePlayer",[],t):"object"==typeof exports?exports.LivePlayer=t():e.LivePlayer=t()}(window,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}({0:function(e,t,n){e.exports=n("qtAY")},"2SVd":function(e,t,n){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},"5oMp":function(e,t,n){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},"8MIm":function(e,t,n){(e.exports=n("I1BE")(void 0)).push([e.i,'.video-js .vjs-big-play-button .vjs-icon-placeholder:before,.video-js .vjs-modal-dialog,.vjs-button>.vjs-icon-placeholder:before,.vjs-modal-dialog .vjs-modal-dialog-content{position:absolute;top:0;left:0;width:100%;height:100%}.video-js .vjs-big-play-button .vjs-icon-placeholder:before,.vjs-button>.vjs-icon-placeholder:before{text-align:center}@font-face{font-family:VideoJS;src:url(data:application/font-woff;charset=utf-8;base64,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) format("woff");font-weight:400;font-style:normal}.video-js .vjs-big-play-button .vjs-icon-placeholder:before,.video-js .vjs-play-control .vjs-icon-placeholder,.vjs-icon-play{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-big-play-button .vjs-icon-placeholder:before,.video-js .vjs-play-control .vjs-icon-placeholder:before,.vjs-icon-play:before{content:"\\F101"}.vjs-icon-play-circle{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-play-circle:before{content:"\\F102"}.video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder,.vjs-icon-pause{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-play-control.vjs-playing .vjs-icon-placeholder:before,.vjs-icon-pause:before{content:"\\F103"}.video-js .vjs-mute-control.vjs-vol-0 .vjs-icon-placeholder,.vjs-icon-volume-mute{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-mute-control.vjs-vol-0 .vjs-icon-placeholder:before,.vjs-icon-volume-mute:before{content:"\\F104"}.video-js .vjs-mute-control.vjs-vol-1 .vjs-icon-placeholder,.vjs-icon-volume-low{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-mute-control.vjs-vol-1 .vjs-icon-placeholder:before,.vjs-icon-volume-low:before{content:"\\F105"}.video-js .vjs-mute-control.vjs-vol-2 .vjs-icon-placeholder,.vjs-icon-volume-mid{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-mute-control.vjs-vol-2 .vjs-icon-placeholder:before,.vjs-icon-volume-mid:before{content:"\\F106"}.video-js .vjs-mute-control .vjs-icon-placeholder,.vjs-icon-volume-high{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-mute-control .vjs-icon-placeholder:before,.vjs-icon-volume-high:before{content:"\\F107"}.video-js .vjs-fullscreen-control .vjs-icon-placeholder,.vjs-icon-fullscreen-enter{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-fullscreen-control .vjs-icon-placeholder:before,.vjs-icon-fullscreen-enter:before{content:"\\F108"}.video-js.vjs-fullscreen .vjs-fullscreen-control .vjs-icon-placeholder,.vjs-icon-fullscreen-exit{font-family:VideoJS;font-weight:400;font-style:normal}.video-js.vjs-fullscreen .vjs-fullscreen-control .vjs-icon-placeholder:before,.vjs-icon-fullscreen-exit:before{content:"\\F109"}.vjs-icon-square{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-square:before{content:"\\F10A"}.vjs-icon-spinner{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-spinner:before{content:"\\F10B"}.video-js.video-js:lang(en-AU) .vjs-subs-caps-button .vjs-icon-placeholder,.video-js.video-js:lang(en-GB) .vjs-subs-caps-button .vjs-icon-placeholder,.video-js.video-js:lang(en-IE) .vjs-subs-caps-button .vjs-icon-placeholder,.video-js.video-js:lang(en-NZ) .vjs-subs-caps-button .vjs-icon-placeholder,.video-js .vjs-subs-caps-button .vjs-icon-placeholder,.video-js .vjs-subtitles-button .vjs-icon-placeholder,.vjs-icon-subtitles{font-family:VideoJS;font-weight:400;font-style:normal}.video-js.video-js:lang(en-AU) .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js.video-js:lang(en-GB) .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js.video-js:lang(en-IE) .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js.video-js:lang(en-NZ) .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js .vjs-subtitles-button .vjs-icon-placeholder:before,.vjs-icon-subtitles:before{content:"\\F10C"}.video-js .vjs-captions-button .vjs-icon-placeholder,.video-js:lang(en) .vjs-subs-caps-button .vjs-icon-placeholder,.video-js:lang(fr-CA) .vjs-subs-caps-button .vjs-icon-placeholder,.vjs-icon-captions{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-captions-button .vjs-icon-placeholder:before,.video-js:lang(en) .vjs-subs-caps-button .vjs-icon-placeholder:before,.video-js:lang(fr-CA) .vjs-subs-caps-button .vjs-icon-placeholder:before,.vjs-icon-captions:before{content:"\\F10D"}.video-js .vjs-chapters-button .vjs-icon-placeholder,.vjs-icon-chapters{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-chapters-button .vjs-icon-placeholder:before,.vjs-icon-chapters:before{content:"\\F10E"}.vjs-icon-share{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-share:before{content:"\\F10F"}.vjs-icon-cog{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-cog:before{content:"\\F110"}.video-js .vjs-play-progress,.video-js .vjs-volume-level,.vjs-icon-circle,.vjs-seek-to-live-control .vjs-icon-placeholder{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-play-progress:before,.video-js .vjs-volume-level:before,.vjs-icon-circle:before,.vjs-seek-to-live-control .vjs-icon-placeholder:before{content:"\\F111"}.vjs-icon-circle-outline{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-circle-outline:before{content:"\\F112"}.vjs-icon-circle-inner-circle{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-circle-inner-circle:before{content:"\\F113"}.vjs-icon-hd{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-hd:before{content:"\\F114"}.video-js .vjs-control.vjs-close-button .vjs-icon-placeholder,.vjs-icon-cancel{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-control.vjs-close-button .vjs-icon-placeholder:before,.vjs-icon-cancel:before{content:"\\F115"}.video-js .vjs-play-control.vjs-ended .vjs-icon-placeholder,.vjs-icon-replay{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-play-control.vjs-ended .vjs-icon-placeholder:before,.vjs-icon-replay:before{content:"\\F116"}.vjs-icon-facebook{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-facebook:before{content:"\\F117"}.vjs-icon-gplus{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-gplus:before{content:"\\F118"}.vjs-icon-linkedin{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-linkedin:before{content:"\\F119"}.vjs-icon-twitter{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-twitter:before{content:"\\F11A"}.vjs-icon-tumblr{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-tumblr:before{content:"\\F11B"}.vjs-icon-pinterest{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-pinterest:before{content:"\\F11C"}.video-js .vjs-descriptions-button .vjs-icon-placeholder,.vjs-icon-audio-description{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-descriptions-button .vjs-icon-placeholder:before,.vjs-icon-audio-description:before{content:"\\F11D"}.video-js .vjs-audio-button .vjs-icon-placeholder,.vjs-icon-audio{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-audio-button .vjs-icon-placeholder:before,.vjs-icon-audio:before{content:"\\F11E"}.vjs-icon-next-item{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-next-item:before{content:"\\F11F"}.vjs-icon-previous-item{font-family:VideoJS;font-weight:400;font-style:normal}.vjs-icon-previous-item:before{content:"\\F120"}.video-js .vjs-picture-in-picture-control .vjs-icon-placeholder,.vjs-icon-picture-in-picture-enter{font-family:VideoJS;font-weight:400;font-style:normal}.video-js .vjs-picture-in-picture-control .vjs-icon-placeholder:before,.vjs-icon-picture-in-picture-enter:before{content:"\\F121"}.video-js.vjs-picture-in-picture .vjs-picture-in-picture-control .vjs-icon-placeholder,.vjs-icon-picture-in-picture-exit{font-family:VideoJS;font-weight:400;font-style:normal}.video-js.vjs-picture-in-picture .vjs-picture-in-picture-control .vjs-icon-placeholder:before,.vjs-icon-picture-in-picture-exit:before{content:"\\F122"}.video-js{display:block;vertical-align:top;box-sizing:border-box;color:#fff;background-color:#000;position:relative;padding:0;font-size:10px;line-height:1;font-weight:400;font-style:normal;font-family:Arial,Helvetica,sans-serif;word-break:normal}.video-js:-moz-full-screen{position:absolute}.video-js:-webkit-full-screen{width:100%!important;height:100%!important}.video-js[tabindex="-1"]{outline:none}.video-js *,.video-js :after,.video-js :before{box-sizing:inherit}.video-js ul{font-family:inherit;font-size:inherit;line-height:inherit;list-style-position:outside;margin:0}.video-js.vjs-4-3,.video-js.vjs-16-9,.video-js.vjs-fluid{width:100%;max-width:100%;height:0}.video-js.vjs-16-9{padding-top:56.25%}.video-js.vjs-4-3{padding-top:75%}.video-js.vjs-fill,.video-js .vjs-tech{width:100%;height:100%}.video-js .vjs-tech{position:absolute;top:0;left:0}body.vjs-full-window{padding:0;margin:0;height:100%}.vjs-full-window .video-js.vjs-fullscreen{position:fixed;overflow:hidden;z-index:1000;left:0;top:0;bottom:0;right:0}.video-js.vjs-fullscreen{width:100%!important;height:100%!important;padding-top:0!important}.video-js.vjs-fullscreen.vjs-user-inactive{cursor:none}.vjs-hidden{display:none!important}.vjs-disabled{opacity:.5;cursor:default}.video-js .vjs-offscreen{height:1px;left:-9999px;position:absolute;top:0;width:1px}.vjs-lock-showing{display:block!important;opacity:1;visibility:visible}.vjs-no-js{padding:20px;color:#fff;background-color:#000;font-size:18px;font-family:Arial,Helvetica,sans-serif;text-align:center;width:300px;height:150px;margin:0 auto}.vjs-no-js a,.vjs-no-js a:visited{color:#66a8cc}.video-js .vjs-big-play-button{font-size:3em;line-height:1.5em;height:1.63332em;width:3em;display:block;position:absolute;top:10px;left:10px;padding:0;cursor:pointer;opacity:1;border:.06666em solid #fff;background-color:#2b333f;background-color:rgba(43,51,63,.7);border-radius:.3em;transition:all .4s}.vjs-big-play-centered .vjs-big-play-button{top:50%;left:50%;margin-top:-.81666em;margin-left:-1.5em}.video-js .vjs-big-play-button:focus,.video-js:hover .vjs-big-play-button{border-color:#fff;background-color:#73859f;background-color:rgba(115,133,159,.5);transition:all 0s}.vjs-controls-disabled .vjs-big-play-button,.vjs-error .vjs-big-play-button,.vjs-has-started .vjs-big-play-button,.vjs-using-native-controls .vjs-big-play-button{display:none}.vjs-has-started.vjs-paused.vjs-show-big-play-button-on-pause .vjs-big-play-button{display:block}.video-js button{background:none;border:none;color:inherit;display:inline-block;font-size:inherit;line-height:inherit;text-transform:none;text-decoration:none;transition:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.vjs-control .vjs-button{width:100%;height:100%}.video-js .vjs-control.vjs-close-button{cursor:pointer;height:3em;position:absolute;right:0;top:.5em;z-index:2}.video-js .vjs-modal-dialog{background:rgba(0,0,0,.8);background:linear-gradient(180deg,rgba(0,0,0,.8),hsla(0,0%,100%,0));overflow:auto}.video-js .vjs-modal-dialog>*{box-sizing:border-box}.vjs-modal-dialog .vjs-modal-dialog-content{font-size:1.2em;line-height:1.5;padding:20px 24px;z-index:1}.vjs-menu-button{cursor:pointer}.vjs-menu-button.vjs-disabled{cursor:default}.vjs-workinghover .vjs-menu-button.vjs-disabled:hover .vjs-menu{display:none}.vjs-menu .vjs-menu-content{display:block;padding:0;margin:0;font-family:Arial,Helvetica,sans-serif;overflow:auto}.vjs-menu .vjs-menu-content>*{box-sizing:border-box}.vjs-scrubbing .vjs-control.vjs-menu-button:hover .vjs-menu{display:none}.vjs-menu li{list-style:none;margin:0;padding:.2em 0;line-height:1.4em;font-size:1.2em;text-align:center;text-transform:lowercase}.js-focus-visible .vjs-menu li.vjs-menu-item:hover,.vjs-menu li.vjs-menu-item:focus,.vjs-menu li.vjs-menu-item:hover{background-color:#73859f;background-color:rgba(115,133,159,.5)}.js-focus-visible .vjs-menu li.vjs-selected:hover,.vjs-menu li.vjs-selected,.vjs-menu li.vjs-selected:focus,.vjs-menu li.vjs-selected:hover{background-color:#fff;color:#2b333f}.vjs-menu li.vjs-menu-title{text-align:center;text-transform:uppercase;font-size:1em;line-height:2em;padding:0;margin:0 0 .3em;font-weight:700;cursor:default}.vjs-menu-button-popup .vjs-menu{display:none;position:absolute;bottom:0;width:10em;left:-3em;height:0;margin-bottom:1.5em;border-top-color:rgba(43,51,63,.7)}.vjs-menu-button-popup .vjs-menu .vjs-menu-content{background-color:#2b333f;background-color:rgba(43,51,63,.7);position:absolute;width:100%;bottom:1.5em;max-height:15em}.vjs-layout-tiny .vjs-menu-button-popup .vjs-menu .vjs-menu-content,.vjs-layout-x-small .vjs-menu-button-popup .vjs-menu .vjs-menu-content{max-height:5em}.vjs-layout-small .vjs-menu-button-popup .vjs-menu .vjs-menu-content{max-height:10em}.vjs-layout-medium .vjs-menu-button-popup .vjs-menu .vjs-menu-content{max-height:14em}.vjs-layout-huge .vjs-menu-button-popup .vjs-menu .vjs-menu-content,.vjs-layout-large .vjs-menu-button-popup .vjs-menu .vjs-menu-content,.vjs-layout-x-large .vjs-menu-button-popup .vjs-menu .vjs-menu-content{max-height:25em}.vjs-menu-button-popup .vjs-menu.vjs-lock-showing,.vjs-workinghover .vjs-menu-button-popup.vjs-hover .vjs-menu{display:block}.video-js .vjs-menu-button-inline{transition:all .4s;overflow:hidden}.video-js .vjs-menu-button-inline:before{width:2.222222222em}.video-js .vjs-menu-button-inline.vjs-slider-active,.video-js .vjs-menu-button-inline:focus,.video-js .vjs-menu-button-inline:hover,.video-js.vjs-no-flex .vjs-menu-button-inline{width:12em}.vjs-menu-button-inline .vjs-menu{opacity:0;height:100%;width:auto;position:absolute;left:4em;top:0;padding:0;margin:0;transition:all .4s}.vjs-menu-button-inline.vjs-slider-active .vjs-menu,.vjs-menu-button-inline:focus .vjs-menu,.vjs-menu-button-inline:hover .vjs-menu{display:block;opacity:1}.vjs-no-flex .vjs-menu-button-inline .vjs-menu{display:block;opacity:1;position:relative;width:auto}.vjs-no-flex .vjs-menu-button-inline.vjs-slider-active .vjs-menu,.vjs-no-flex .vjs-menu-button-inline:focus .vjs-menu,.vjs-no-flex .vjs-menu-button-inline:hover .vjs-menu{width:auto}.vjs-menu-button-inline .vjs-menu-content{width:auto;height:100%;margin:0;overflow:hidden}.video-js .vjs-control-bar{display:none;width:100%;position:absolute;bottom:0;left:0;right:0;height:3em;background-color:#2b333f;background-color:rgba(43,51,63,.7)}.vjs-has-started .vjs-control-bar{display:flex;visibility:visible;opacity:1;transition:visibility .1s,opacity .1s}.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar{visibility:visible;opacity:0;transition:visibility 1s,opacity 1s}.vjs-controls-disabled .vjs-control-bar,.vjs-error .vjs-control-bar,.vjs-using-native-controls .vjs-control-bar{display:none!important}.vjs-audio.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar{opacity:1;visibility:visible}.vjs-has-started.vjs-no-flex .vjs-control-bar{display:table}.video-js .vjs-control{position:relative;text-align:center;margin:0;padding:0;height:100%;width:4em;flex:none}.vjs-button>.vjs-icon-placeholder:before{font-size:1.8em;line-height:1.67}.video-js .vjs-control:focus,.video-js .vjs-control:focus:before,.video-js .vjs-control:hover:before{text-shadow:0 0 1em #fff}.video-js .vjs-control-text{border:0;clip:rect(0 0 0 0);height:1px;overflow:hidden;padding:0;position:absolute;width:1px}.vjs-no-flex .vjs-control{display:table-cell;vertical-align:middle}.video-js .vjs-custom-control-spacer{display:none}.video-js .vjs-progress-control{cursor:pointer;flex:auto;display:flex;align-items:center;min-width:4em;touch-action:none}.video-js .vjs-progress-control.disabled{cursor:default}.vjs-live .vjs-progress-control{display:none}.vjs-liveui .vjs-progress-control{display:flex;align-items:center}.vjs-no-flex .vjs-progress-control{width:auto}.video-js .vjs-progress-holder{flex:auto;transition:all .2s;height:.3em}.video-js .vjs-progress-control .vjs-progress-holder{margin:0 10px}.video-js .vjs-progress-control:hover .vjs-progress-holder{font-size:1.6666666667em}.video-js .vjs-progress-control:hover .vjs-progress-holder.disabled{font-size:1em}.video-js .vjs-progress-holder .vjs-load-progress,.video-js .vjs-progress-holder .vjs-load-progress div,.video-js .vjs-progress-holder .vjs-play-progress{position:absolute;display:block;height:100%;margin:0;padding:0;width:0}.video-js .vjs-play-progress{background-color:#fff}.video-js .vjs-play-progress:before{font-size:.9em;position:absolute;right:-.5em;top:-.3333333333em;z-index:1}.video-js .vjs-load-progress{background:rgba(115,133,159,.5)}.video-js .vjs-load-progress div{background:rgba(115,133,159,.75)}.video-js .vjs-time-tooltip{background-color:#fff;background-color:hsla(0,0%,100%,.8);border-radius:.3em;color:#000;float:right;font-family:Arial,Helvetica,sans-serif;font-size:1em;padding:6px 8px 8px;pointer-events:none;position:absolute;top:-3.4em;visibility:hidden;z-index:1}.video-js .vjs-progress-holder:focus .vjs-time-tooltip{display:none}.video-js .vjs-progress-control:hover .vjs-progress-holder:focus .vjs-time-tooltip,.video-js .vjs-progress-control:hover .vjs-time-tooltip{display:block;font-size:.6em;visibility:visible}.video-js .vjs-progress-control.disabled:hover .vjs-time-tooltip{font-size:1em}.video-js .vjs-progress-control .vjs-mouse-display{display:none;position:absolute;width:1px;height:100%;background-color:#000;z-index:1}.vjs-no-flex .vjs-progress-control .vjs-mouse-display{z-index:0}.video-js .vjs-progress-control:hover .vjs-mouse-display{display:block}.video-js.vjs-user-inactive .vjs-progress-control .vjs-mouse-display{visibility:hidden;opacity:0;transition:visibility 1s,opacity 1s}.video-js.vjs-user-inactive.vjs-no-flex .vjs-progress-control .vjs-mouse-display{display:none}.vjs-mouse-display .vjs-time-tooltip{color:#fff;background-color:#000;background-color:rgba(0,0,0,.8)}.video-js .vjs-slider{position:relative;cursor:pointer;padding:0;margin:0 .45em;-webkit-touch-callout:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:#73859f;background-color:rgba(115,133,159,.5)}.video-js .vjs-slider.disabled{cursor:default}.video-js .vjs-slider:focus{text-shadow:0 0 1em #fff;box-shadow:0 0 1em #fff}.video-js .vjs-mute-control{cursor:pointer;flex:none}.video-js .vjs-volume-control{cursor:pointer;margin-right:1em;display:flex}.video-js .vjs-volume-control.vjs-volume-horizontal{width:5em}.video-js .vjs-volume-panel .vjs-volume-control{visibility:visible;opacity:0;width:1px;height:1px;margin-left:-1px}.video-js .vjs-volume-panel{transition:width 1s}.video-js .vjs-volume-panel.vjs-hover .vjs-mute-control~.vjs-volume-control,.video-js .vjs-volume-panel.vjs-hover .vjs-volume-control,.video-js .vjs-volume-panel .vjs-volume-control.vjs-slider-active,.video-js .vjs-volume-panel .vjs-volume-control:active,.video-js .vjs-volume-panel:active .vjs-volume-control,.video-js .vjs-volume-panel:focus .vjs-volume-control{visibility:visible;opacity:1;position:relative;transition:visibility .1s,opacity .1s,height .1s,width .1s,left 0s,top 0s}.video-js .vjs-volume-panel.vjs-hover .vjs-mute-control~.vjs-volume-control.vjs-volume-horizontal,.video-js .vjs-volume-panel.vjs-hover .vjs-volume-control.vjs-volume-horizontal,.video-js .vjs-volume-panel .vjs-volume-control.vjs-slider-active.vjs-volume-horizontal,.video-js .vjs-volume-panel .vjs-volume-control:active.vjs-volume-horizontal,.video-js .vjs-volume-panel:active .vjs-volume-control.vjs-volume-horizontal,.video-js .vjs-volume-panel:focus .vjs-volume-control.vjs-volume-horizontal{width:5em;height:3em}.video-js .vjs-volume-panel.vjs-hover .vjs-mute-control~.vjs-volume-control.vjs-volume-vertical,.video-js .vjs-volume-panel.vjs-hover .vjs-volume-control.vjs-volume-vertical,.video-js .vjs-volume-panel .vjs-volume-control.vjs-slider-active.vjs-volume-vertical,.video-js .vjs-volume-panel .vjs-volume-control:active.vjs-volume-vertical,.video-js .vjs-volume-panel:active .vjs-volume-control.vjs-volume-vertical,.video-js .vjs-volume-panel:focus .vjs-volume-control.vjs-volume-vertical{left:-3.5em;transition:left 0s}.video-js .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-hover,.video-js .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-slider-active,.video-js .vjs-volume-panel.vjs-volume-panel-horizontal:active{width:9em;transition:width .1s}.video-js .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-mute-toggle-only{width:4em}.video-js .vjs-volume-panel .vjs-volume-control.vjs-volume-vertical{height:8em;width:3em;left:-3000em;transition:visibility 1s,opacity 1s,height 1s 1s,width 1s 1s,left 1s 1s,top 1s 1s}.video-js .vjs-volume-panel .vjs-volume-control.vjs-volume-horizontal{transition:visibility 1s,opacity 1s,height 1s 1s,width 1s,left 1s 1s,top 1s 1s}.video-js.vjs-no-flex .vjs-volume-panel .vjs-volume-control.vjs-volume-horizontal{width:5em;height:3em;visibility:visible;opacity:1;position:relative;transition:none}.video-js.vjs-no-flex .vjs-volume-control.vjs-volume-vertical,.video-js.vjs-no-flex .vjs-volume-panel .vjs-volume-control.vjs-volume-vertical{position:absolute;bottom:3em;left:.5em}.video-js .vjs-volume-panel{display:flex}.video-js .vjs-volume-bar{margin:1.35em .45em}.vjs-volume-bar.vjs-slider-horizontal{width:5em;height:.3em}.vjs-volume-bar.vjs-slider-vertical{width:.3em;height:5em;margin:1.35em auto}.video-js .vjs-volume-level{position:absolute;bottom:0;left:0;background-color:#fff}.video-js .vjs-volume-level:before{position:absolute;font-size:.9em}.vjs-slider-vertical .vjs-volume-level{width:.3em}.vjs-slider-vertical .vjs-volume-level:before{top:-.5em;left:-.3em}.vjs-slider-horizontal .vjs-volume-level{height:.3em}.vjs-slider-horizontal .vjs-volume-level:before{top:-.3em;right:-.5em}.video-js .vjs-volume-panel.vjs-volume-panel-vertical{width:4em}.vjs-volume-bar.vjs-slider-vertical .vjs-volume-level{height:100%}.vjs-volume-bar.vjs-slider-horizontal .vjs-volume-level{width:100%}.video-js .vjs-volume-vertical{width:3em;height:8em;bottom:8em;background-color:#2b333f;background-color:rgba(43,51,63,.7)}.video-js .vjs-volume-horizontal .vjs-menu{left:-2em}.vjs-poster{display:inline-block;vertical-align:middle;background-repeat:no-repeat;background-position:50% 50%;background-size:contain;background-color:#000;cursor:pointer;margin:0;padding:0;position:absolute;top:0;right:0;bottom:0;left:0;height:100%}.vjs-has-started .vjs-poster{display:none}.vjs-audio.vjs-has-started .vjs-poster{display:block}.vjs-using-native-controls .vjs-poster{display:none}.video-js .vjs-live-control{display:flex;align-items:flex-start;flex:auto;font-size:1em;line-height:3em}.vjs-no-flex .vjs-live-control{display:table-cell;width:auto;text-align:left}.video-js.vjs-liveui .vjs-live-control,.video-js:not(.vjs-live) .vjs-live-control{display:none}.video-js .vjs-seek-to-live-control{cursor:pointer;flex:none;display:inline-flex;height:100%;padding-left:.5em;padding-right:.5em;font-size:1em;line-height:3em;width:auto;min-width:4em}.vjs-no-flex .vjs-seek-to-live-control{display:table-cell;width:auto;text-align:left}.video-js.vjs-live:not(.vjs-liveui) .vjs-seek-to-live-control,.video-js:not(.vjs-live) .vjs-seek-to-live-control{display:none}.vjs-seek-to-live-control.vjs-control.vjs-at-live-edge{cursor:auto}.vjs-seek-to-live-control .vjs-icon-placeholder{margin-right:.5em;color:#888}.vjs-seek-to-live-control.vjs-control.vjs-at-live-edge .vjs-icon-placeholder{color:red}.video-js .vjs-time-control{flex:none;font-size:1em;line-height:3em;min-width:2em;width:auto;padding-left:1em;padding-right:1em}.video-js .vjs-current-time,.video-js .vjs-duration,.vjs-live .vjs-time-control,.vjs-no-flex .vjs-current-time,.vjs-no-flex .vjs-duration{display:none}.vjs-time-divider{display:none;line-height:3em}.vjs-live .vjs-time-divider{display:none}.video-js .vjs-play-control{cursor:pointer}.video-js .vjs-play-control .vjs-icon-placeholder{flex:none}.vjs-text-track-display{position:absolute;bottom:3em;left:0;right:0;top:0;pointer-events:none}.video-js.vjs-user-inactive.vjs-playing .vjs-text-track-display{bottom:1em}.video-js .vjs-text-track{font-size:1.4em;text-align:center;margin-bottom:.1em}.vjs-subtitles{color:#fff}.vjs-captions{color:#fc6}.vjs-tt-cue{display:block}video::-webkit-media-text-track-display{transform:translateY(-3em)}.video-js.vjs-user-inactive.vjs-playing video::-webkit-media-text-track-display{transform:translateY(-1.5em)}.video-js .vjs-fullscreen-control,.video-js .vjs-picture-in-picture-control{cursor:pointer;flex:none}.vjs-playback-rate .vjs-playback-rate-value,.vjs-playback-rate>.vjs-menu-button{position:absolute;top:0;left:0;width:100%;height:100%}.vjs-playback-rate .vjs-playback-rate-value{pointer-events:none;font-size:1.5em;line-height:2;text-align:center}.vjs-playback-rate .vjs-menu{width:4em;left:0}.vjs-error .vjs-error-display .vjs-modal-dialog-content{font-size:1.4em;text-align:center}.vjs-error .vjs-error-display:before{color:#fff;content:"X";font-family:Arial,Helvetica,sans-serif;font-size:4em;left:0;line-height:1;margin-top:-.5em;position:absolute;text-shadow:.05em .05em .1em #000;text-align:center;top:50%;vertical-align:middle;width:100%}.vjs-loading-spinner{display:none;position:absolute;top:50%;left:50%;margin:-25px 0 0 -25px;opacity:.85;text-align:left;border:6px solid rgba(43,51,63,.7);box-sizing:border-box;background-clip:padding-box;width:50px;height:50px;border-radius:25px;visibility:hidden}.vjs-seeking .vjs-loading-spinner,.vjs-waiting .vjs-loading-spinner{display:block;-webkit-animation:vjs-spinner-show 0s linear .3s forwards;animation:vjs-spinner-show 0s linear .3s forwards}.vjs-loading-spinner:after,.vjs-loading-spinner:before{content:"";position:absolute;margin:-6px;box-sizing:inherit;width:inherit;height:inherit;border-radius:inherit;opacity:1;border:inherit;border-color:transparent;border-top-color:#fff}.vjs-seeking .vjs-loading-spinner:after,.vjs-seeking .vjs-loading-spinner:before,.vjs-waiting .vjs-loading-spinner:after,.vjs-waiting .vjs-loading-spinner:before{-webkit-animation:vjs-spinner-spin 1.1s cubic-bezier(.6,.2,0,.8) infinite,vjs-spinner-fade 1.1s linear infinite;animation:vjs-spinner-spin 1.1s cubic-bezier(.6,.2,0,.8) infinite,vjs-spinner-fade 1.1s linear infinite}.vjs-seeking .vjs-loading-spinner:before,.vjs-waiting .vjs-loading-spinner:before{border-top-color:#fff}.vjs-seeking .vjs-loading-spinner:after,.vjs-waiting .vjs-loading-spinner:after{border-top-color:#fff;-webkit-animation-delay:.44s;animation-delay:.44s}@keyframes vjs-spinner-show{to{visibility:visible}}@-webkit-keyframes vjs-spinner-show{to{visibility:visible}}@keyframes vjs-spinner-spin{to{transform:rotate(1turn)}}@-webkit-keyframes vjs-spinner-spin{to{-webkit-transform:rotate(1turn)}}@keyframes vjs-spinner-fade{0%{border-top-color:#73859f}20%{border-top-color:#73859f}35%{border-top-color:#fff}60%{border-top-color:#73859f}to{border-top-color:#73859f}}@-webkit-keyframes vjs-spinner-fade{0%{border-top-color:#73859f}20%{border-top-color:#73859f}35%{border-top-color:#fff}60%{border-top-color:#73859f}to{border-top-color:#73859f}}.vjs-chapters-button .vjs-menu ul{width:24em}.video-js .vjs-subs-caps-button+.vjs-menu .vjs-captions-menu-item .vjs-menu-item-text .vjs-icon-placeholder{vertical-align:middle;display:inline-block;margin-bottom:-.1em}.video-js .vjs-subs-caps-button+.vjs-menu .vjs-captions-menu-item .vjs-menu-item-text .vjs-icon-placeholder:before{font-family:VideoJS;content:"\\F10D";font-size:1.5em;line-height:inherit}.video-js .vjs-audio-button+.vjs-menu .vjs-main-desc-menu-item .vjs-menu-item-text .vjs-icon-placeholder{vertical-align:middle;display:inline-block;margin-bottom:-.1em}.video-js .vjs-audio-button+.vjs-menu .vjs-main-desc-menu-item .vjs-menu-item-text .vjs-icon-placeholder:before{font-family:VideoJS;content:" \\F11D";font-size:1.5em;line-height:inherit}.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-audio-button,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-captions-button,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-chapters-button,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-current-time,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-descriptions-button,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-duration,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-playback-rate,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-remaining-time,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-subtitles-button,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-time-divider,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-volume-control,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-audio-button,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-captions-button,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-chapters-button,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-current-time,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-descriptions-button,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-duration,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-playback-rate,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-remaining-time,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-subtitles-button,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-time-divider,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-volume-control,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-audio-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-captions-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-chapters-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-current-time,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-descriptions-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-duration,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-playback-rate,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-remaining-time,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-subtitles-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-time-divider,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-volume-control{display:none}.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-slider-active,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-volume-panel.vjs-volume-panel-horizontal:active,.video-js:not(.vjs-fullscreen).vjs-layout-small .vjs-volume-panel.vjs-volume-panel-horizontal:hover,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-slider-active,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-volume-panel.vjs-volume-panel-horizontal:active,.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-volume-panel.vjs-volume-panel-horizontal:hover,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-volume-panel.vjs-volume-panel-horizontal.vjs-slider-active,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-volume-panel.vjs-volume-panel-horizontal:active,.video-js:not(.vjs-fullscreen).vjs-layout-x-small .vjs-volume-panel.vjs-volume-panel-horizontal:hover{width:auto}.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-subs-caps-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small:not(.vjs-live) .vjs-subs-caps-button,.video-js:not(.vjs-fullscreen).vjs-layout-x-small:not(.vjs-liveui) .vjs-subs-caps-button{display:none}.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-custom-control-spacer,.video-js:not(.vjs-fullscreen).vjs-layout-x-small.vjs-liveui .vjs-custom-control-spacer{flex:auto;display:block}.video-js:not(.vjs-fullscreen).vjs-layout-tiny.vjs-no-flex .vjs-custom-control-spacer,.video-js:not(.vjs-fullscreen).vjs-layout-x-small.vjs-liveui.vjs-no-flex .vjs-custom-control-spacer{width:auto}.video-js:not(.vjs-fullscreen).vjs-layout-tiny .vjs-progress-control,.video-js:not(.vjs-fullscreen).vjs-layout-x-small.vjs-liveui .vjs-progress-control{display:none}.vjs-modal-dialog.vjs-text-track-settings{background-color:#2b333f;background-color:rgba(43,51,63,.75);color:#fff;height:70%}.vjs-text-track-settings .vjs-modal-dialog-content{display:table}.vjs-text-track-settings .vjs-track-settings-colors,.vjs-text-track-settings .vjs-track-settings-controls,.vjs-text-track-settings .vjs-track-settings-font{display:table-cell}.vjs-text-track-settings .vjs-track-settings-controls{text-align:right;vertical-align:bottom}@supports (display:grid){.vjs-text-track-settings .vjs-modal-dialog-content{display:grid;grid-template-columns:1fr 1fr;grid-template-rows:1fr;padding:20px 24px 0}.vjs-track-settings-controls .vjs-default-button{margin-bottom:20px}.vjs-text-track-settings .vjs-track-settings-controls{grid-column:1/-1}.vjs-layout-small .vjs-text-track-settings .vjs-modal-dialog-content,.vjs-layout-tiny .vjs-text-track-settings .vjs-modal-dialog-content,.vjs-layout-x-small .vjs-text-track-settings .vjs-modal-dialog-content{grid-template-columns:1fr}}.vjs-track-setting>select{margin-right:1em;margin-bottom:.5em}.vjs-text-track-settings fieldset{margin:5px;padding:3px;border:none}.vjs-text-track-settings fieldset span{display:inline-block}.vjs-text-track-settings fieldset span>select{max-width:7.3em}.vjs-text-track-settings legend{color:#fff;margin:0 0 5px}.vjs-text-track-settings .vjs-label{position:absolute;clip:rect(1px 1px 1px 1px);clip:rect(1px,1px,1px,1px);display:block;margin:0 0 5px;padding:0;border:0;height:1px;width:1px;overflow:hidden}.vjs-track-settings-controls button:active,.vjs-track-settings-controls button:focus{outline-style:solid;outline-width:medium;background-image:linear-gradient(0deg,#fff 88%,#73859f)}.vjs-track-settings-controls button:hover{color:rgba(43,51,63,.75)}.vjs-track-settings-controls button{background-color:#fff;background-image:linear-gradient(-180deg,#fff 88%,#73859f);color:#2b333f;cursor:pointer;border-radius:2px}.vjs-track-settings-controls .vjs-default-button{margin-right:1em}@media print{.video-js>:not(.vjs-tech):not(.vjs-poster){visibility:hidden}}.vjs-resize-manager{position:absolute;top:0;left:0;width:100%;height:100%;border:none;z-index:-1000}.js-focus-visible .video-js :focus:not(.focus-visible),.video-js .vjs-menu :focus:not(:focus-visible),.video-js :focus:not(:focus-visible){outline:none;background:none}',""])},"8eWk":function(e,t,n){var r=n("LboF"),o=n("qFNJ");"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:"head",singleton:!1};r(o,i);e.exports=o.locals||{}},"8oxB":function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var u,l=[],c=!1,f=-1;function v(){c&&u&&(c=!1,u.length?l=u.concat(l):f=-1,l.length&&p())}function p(){if(!c){var e=a(v);c=!0;for(var t=l.length;t;){for(u=l,l=[];++f<t;)u&&u[f].run();f=-1,t=l.length}u=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function h(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new d(e,t)),1!==l.length||c||a(p)},d.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=h,o.addListener=h,o.once=h,o.off=h,o.removeListener=h,o.removeAllListeners=h,o.emit=h,o.prependListener=h,o.prependOnceListener=h,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},"9rSQ":function(e,t,n){"use strict";var r=n("xTJ+");function o(){this.handlers=[]}o.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},CgaS:function(e,t,n){"use strict";var r=n("JEQr"),o=n("xTJ+"),i=n("9rSQ"),s=n("UnBK");function a(e){this.defaults=e,this.interceptors={request:new i,response:new i}}a.prototype.request=function(e){"string"==typeof e&&(e=o.merge({url:arguments[0]},arguments[1])),(e=o.merge(r,this.defaults,{method:"get"},e)).method=e.method.toLowerCase();var t=[s,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},o.forEach(["delete","get","head","options"],(function(e){a.prototype[e]=function(t,n){return this.request(o.merge(n||{},{method:e,url:t}))}})),o.forEach(["post","put","patch"],(function(e){a.prototype[e]=function(t,n,r){return this.request(o.merge(r||{},{method:e,url:t,data:n}))}})),e.exports=a},DfZB:function(e,t,n){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},HSsa:function(e,t,n){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},I1BE:function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n=e[1]||"",r=e[3];if(!r)return n;if(t&&"function"==typeof btoa){var o=(s=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(s))))+" */"),i=r.sources.map((function(e){return"/*# sourceURL="+r.sourceRoot+e+" */"}));return[n].concat(i).concat([o]).join("\n")}var s;return[n].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(r[i]=!0)}for(o=0;o<e.length;o++){var s=e[o];"number"==typeof s[0]&&r[s[0]]||(n&&!s[2]?s[2]=n:n&&(s[2]="("+s[2]+") and ("+n+")"),t.push(s))}},t}},Iab2:function(e,t,n){(function(n){var r,o,i;o=[],void 0===(i="function"==typeof(r=function(){"use strict";function t(e,t){return void 0===t?t={autoBom:!1}:"object"!=typeof t&&(console.warn("Deprecated: Expected third argument to be a object"),t={autoBom:!t}),t.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob(["\ufeff",e],{type:e.type}):e}function r(e,t,n){var r=new XMLHttpRequest;r.open("GET",e),r.responseType="blob",r.onload=function(){a(r.response,t,n)},r.onerror=function(){console.error("could not download file")},r.send()}function o(e){var t=new XMLHttpRequest;return t.open("HEAD",e,!1),t.send(),200<=t.status&&299>=t.status}function i(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}var s="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof n&&n.global===n?n:void 0,a=s.saveAs||("object"!=typeof window||window!==s?function(){}:"download"in HTMLAnchorElement.prototype?function(e,t,n){var a=s.URL||s.webkitURL,u=document.createElement("a");t=t||e.name||"download",u.download=t,u.rel="noopener","string"==typeof e?(u.href=e,u.origin===location.origin?i(u):o(u.href)?r(e,t,n):i(u,u.target="_blank")):(u.href=a.createObjectURL(e),setTimeout((function(){a.revokeObjectURL(u.href)}),4e4),setTimeout((function(){i(u)}),0))}:"msSaveOrOpenBlob"in navigator?function(e,n,s){if(n=n||e.name||"download","string"!=typeof e)navigator.msSaveOrOpenBlob(t(e,s),n);else if(o(e))r(e,n,s);else{var a=document.createElement("a");a.href=e,a.target="_blank",setTimeout((function(){i(a)}))}}:function(e,t,n,o){if((o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading..."),"string"==typeof e)return r(e,t,n);var i="application/octet-stream"===e.type,a=/constructor/i.test(s.HTMLElement)||s.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||i&&a)&&"object"==typeof FileReader){var l=new FileReader;l.onloadend=function(){var e=l.result;e=u?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location=e,o=null},l.readAsDataURL(e)}else{var c=s.URL||s.webkitURL,f=c.createObjectURL(e);o?o.location=f:location.href=f,o=null,setTimeout((function(){c.revokeObjectURL(f)}),4e4)}});s.saveAs=a.saveAs=a,e.exports=a})?r.apply(t,o):r)||(e.exports=i)}).call(this,n("yLpj"))},JEQr:function(e,t,n){"use strict";(function(t){var r=n("xTJ+"),o=n("yK9s"),i={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,u={adapter:(("undefined"!=typeof XMLHttpRequest||void 0!==t)&&(a=n("tQ2B")),a),transformRequest:[function(e,t){return o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(s(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,validateStatus:function(e){return e>=200&&e<300}};u.headers={common:{Accept:"application/json, text/plain, */*"}},r.forEach(["delete","get","head"],(function(e){u.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){u.headers[e]=r.merge(i)})),e.exports=u}).call(this,n("8oxB"))},LYNF:function(e,t,n){"use strict";var r=n("OH9c");e.exports=function(e,t,n,o,i){var s=new Error(e);return r(s,t,n,o,i)}},LboF:function(e,t,n){"use strict";var r,o=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},i=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),s=[];function a(e){for(var t=-1,n=0;n<s.length;n++)if(s[n].identifier===e){t=n;break}return t}function u(e,t){for(var n={},r=[],o=0;o<e.length;o++){var i=e[o],u=t.base?i[0]+t.base:i[0],l=n[u]||0,c="".concat(u," ").concat(l);n[u]=l+1;var f=a(c),v={css:i[1],media:i[2],sourceMap:i[3]};-1!==f?(s[f].references++,s[f].updater(v)):s.push({identifier:c,updater:j(v,t),references:1}),r.push(c)}return r}function l(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var o=n.nc;o&&(r.nonce=o)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var s=i(e.insert||"head");if(!s)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");s.appendChild(t)}return t}var c,f=(c=[],function(e,t){return c[e]=t,c.filter(Boolean).join("\n")});function v(e,t,n,r){var o=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=f(t,o);else{var i=document.createTextNode(o),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(i,s[t]):e.appendChild(i)}}function p(e,t,n){var r=n.css,o=n.media,i=n.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),i&&btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var d=null,h=0;function j(e,t){var n,r,o;if(t.singleton){var i=h++;n=d||(d=l(t)),r=v.bind(null,n,i,!1),o=v.bind(null,n,i,!0)}else n=l(t),r=p.bind(null,n,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=o());var n=u(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var o=a(n[r]);s[o].references--}for(var i=u(e,t),l=0;l<n.length;l++){var c=a(n[l]);0===s[c].references&&(s[c].updater(),s.splice(c,1))}n=i}}}},Lmem:function(e,t,n){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},LvDl:function(e,t,n){(function(e,r){var o;(function(){var i,s="Expected a function",a="__lodash_hash_undefined__",u="__lodash_placeholder__",l=16,c=32,f=64,v=128,p=256,d=1/0,h=9007199254740991,j=NaN,m=4294967295,y=[["ary",v],["bind",1],["bindKey",2],["curry",8],["curryRight",l],["flip",512],["partial",c],["partialRight",f],["rearg",p]],g="[object Arguments]",b="[object Array]",w="[object Boolean]",_="[object Date]",x="[object Error]",A="[object Function]",k="[object GeneratorFunction]",S="[object Map]",B="[object Number]",T="[object Object]",O="[object Promise]",C="[object RegExp]",R="[object Set]",z="[object String]",E="[object Symbol]",F="[object WeakMap]",L="[object ArrayBuffer]",I="[object DataView]",J="[object Float32Array]",U="[object Float64Array]",q="[object Int8Array]",D="[object Int16Array]",M="[object Int32Array]",N="[object Uint8Array]",V="[object Uint8ClampedArray]",W="[object Uint16Array]",P="[object Uint32Array]",H=/\b__p \+= '';/g,Y=/\b(__p \+=) '' \+/g,Q=/(__e\(.*?\)|\b__t\)) \+\n'';/g,G=/&(?:amp|lt|gt|quot|#39);/g,Z=/[&<>"']/g,K=RegExp(G.source),X=RegExp(Z.source),$=/<%-([\s\S]+?)%>/g,ee=/<%([\s\S]+?)%>/g,te=/<%=([\s\S]+?)%>/g,ne=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,re=/^\w*$/,oe=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ie=/[\\^$.*+?()[\]{}|]/g,se=RegExp(ie.source),ae=/^\s+|\s+$/g,ue=/^\s+/,le=/\s+$/,ce=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,fe=/\{\n\/\* \[wrapped with (.+)\] \*/,ve=/,? & /,pe=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,de=/\\(\\)?/g,he=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,je=/\w*$/,me=/^[-+]0x[0-9a-f]+$/i,ye=/^0b[01]+$/i,ge=/^\[object .+?Constructor\]$/,be=/^0o[0-7]+$/i,we=/^(?:0|[1-9]\d*)$/,_e=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,xe=/($^)/,Ae=/['\n\r\u2028\u2029\\]/g,ke="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Se="\\u2700-\\u27bf",Be="a-z\\xdf-\\xf6\\xf8-\\xff",Te="A-Z\\xc0-\\xd6\\xd8-\\xde",Oe="\\ufe0e\\ufe0f",Ce="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Re="['’]",ze="[\\ud800-\\udfff]",Ee="["+Ce+"]",Fe="["+ke+"]",Le="\\d+",Ie="[\\u2700-\\u27bf]",Je="["+Be+"]",Ue="[^\\ud800-\\udfff"+Ce+Le+Se+Be+Te+"]",qe="\\ud83c[\\udffb-\\udfff]",De="[^\\ud800-\\udfff]",Me="(?:\\ud83c[\\udde6-\\uddff]){2}",Ne="[\\ud800-\\udbff][\\udc00-\\udfff]",Ve="["+Te+"]",We="(?:"+Je+"|"+Ue+")",Pe="(?:"+Ve+"|"+Ue+")",He="(?:['’](?:d|ll|m|re|s|t|ve))?",Ye="(?:['’](?:D|LL|M|RE|S|T|VE))?",Qe="(?:"+Fe+"|"+qe+")"+"?",Ge="[\\ufe0e\\ufe0f]?",Ze=Ge+Qe+("(?:\\u200d(?:"+[De,Me,Ne].join("|")+")"+Ge+Qe+")*"),Ke="(?:"+[Ie,Me,Ne].join("|")+")"+Ze,Xe="(?:"+[De+Fe+"?",Fe,Me,Ne,ze].join("|")+")",$e=RegExp(Re,"g"),et=RegExp(Fe,"g"),tt=RegExp(qe+"(?="+qe+")|"+Xe+Ze,"g"),nt=RegExp([Ve+"?"+Je+"+"+He+"(?="+[Ee,Ve,"$"].join("|")+")",Pe+"+"+Ye+"(?="+[Ee,Ve+We,"$"].join("|")+")",Ve+"?"+We+"+"+He,Ve+"+"+Ye,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Le,Ke].join("|"),"g"),rt=RegExp("[\\u200d\\ud800-\\udfff"+ke+Oe+"]"),ot=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,it=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],st=-1,at={};at[J]=at[U]=at[q]=at[D]=at[M]=at[N]=at[V]=at[W]=at[P]=!0,at[g]=at[b]=at[L]=at[w]=at[I]=at[_]=at[x]=at[A]=at[S]=at[B]=at[T]=at[C]=at[R]=at[z]=at[F]=!1;var ut={};ut[g]=ut[b]=ut[L]=ut[I]=ut[w]=ut[_]=ut[J]=ut[U]=ut[q]=ut[D]=ut[M]=ut[S]=ut[B]=ut[T]=ut[C]=ut[R]=ut[z]=ut[E]=ut[N]=ut[V]=ut[W]=ut[P]=!0,ut[x]=ut[A]=ut[F]=!1;var lt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ct=parseFloat,ft=parseInt,vt="object"==typeof e&&e&&e.Object===Object&&e,pt="object"==typeof self&&self&&self.Object===Object&&self,dt=vt||pt||Function("return this")(),ht=t&&!t.nodeType&&t,jt=ht&&"object"==typeof r&&r&&!r.nodeType&&r,mt=jt&&jt.exports===ht,yt=mt&&vt.process,gt=function(){try{var e=jt&&jt.require&&jt.require("util").types;return e||yt&&yt.binding&&yt.binding("util")}catch(e){}}(),bt=gt&&gt.isArrayBuffer,wt=gt&&gt.isDate,_t=gt&&gt.isMap,xt=gt&&gt.isRegExp,At=gt&&gt.isSet,kt=gt&&gt.isTypedArray;function St(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function Bt(e,t,n,r){for(var o=-1,i=null==e?0:e.length;++o<i;){var s=e[o];t(r,s,n(s),e)}return r}function Tt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Ot(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function Ct(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Rt(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var s=e[n];t(s,n,e)&&(i[o++]=s)}return i}function zt(e,t){return!!(null==e?0:e.length)&&Nt(e,t,0)>-1}function Et(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}function Ft(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}function Lt(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function It(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function Jt(e,t,n,r){var o=null==e?0:e.length;for(r&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}function Ut(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var qt=Ht("length");function Dt(e,t,n){var r;return n(e,(function(e,n,o){if(t(e,n,o))return r=n,!1})),r}function Mt(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}function Nt(e,t,n){return t==t?function(e,t,n){var r=n-1,o=e.length;for(;++r<o;)if(e[r]===t)return r;return-1}(e,t,n):Mt(e,Wt,n)}function Vt(e,t,n,r){for(var o=n-1,i=e.length;++o<i;)if(r(e[o],t))return o;return-1}function Wt(e){return e!=e}function Pt(e,t){var n=null==e?0:e.length;return n?Gt(e,t)/n:j}function Ht(e){return function(t){return null==t?i:t[e]}}function Yt(e){return function(t){return null==e?i:e[t]}}function Qt(e,t,n,r,o){return o(e,(function(e,o,i){n=r?(r=!1,e):t(n,e,o,i)})),n}function Gt(e,t){for(var n,r=-1,o=e.length;++r<o;){var s=t(e[r]);s!==i&&(n=n===i?s:n+s)}return n}function Zt(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function Kt(e){return function(t){return e(t)}}function Xt(e,t){return Ft(t,(function(t){return e[t]}))}function $t(e,t){return e.has(t)}function en(e,t){for(var n=-1,r=e.length;++n<r&&Nt(t,e[n],0)>-1;);return n}function tn(e,t){for(var n=e.length;n--&&Nt(t,e[n],0)>-1;);return n}function nn(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}var rn=Yt({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),on=Yt({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function sn(e){return"\\"+lt[e]}function an(e){return rt.test(e)}function un(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function ln(e,t){return function(n){return e(t(n))}}function cn(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var s=e[n];s!==t&&s!==u||(e[n]=u,i[o++]=n)}return i}function fn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}function vn(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=[e,e]})),n}function pn(e){return an(e)?function(e){var t=tt.lastIndex=0;for(;tt.test(e);)++t;return t}(e):qt(e)}function dn(e){return an(e)?function(e){return e.match(tt)||[]}(e):function(e){return e.split("")}(e)}var hn=Yt({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var jn=function e(t){var n,r=(t=null==t?dt:jn.defaults(dt.Object(),t,jn.pick(dt,it))).Array,o=t.Date,ke=t.Error,Se=t.Function,Be=t.Math,Te=t.Object,Oe=t.RegExp,Ce=t.String,Re=t.TypeError,ze=r.prototype,Ee=Se.prototype,Fe=Te.prototype,Le=t["__core-js_shared__"],Ie=Ee.toString,Je=Fe.hasOwnProperty,Ue=0,qe=(n=/[^.]+$/.exec(Le&&Le.keys&&Le.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",De=Fe.toString,Me=Ie.call(Te),Ne=dt._,Ve=Oe("^"+Ie.call(Je).replace(ie,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),We=mt?t.Buffer:i,Pe=t.Symbol,He=t.Uint8Array,Ye=We?We.allocUnsafe:i,Qe=ln(Te.getPrototypeOf,Te),Ge=Te.create,Ze=Fe.propertyIsEnumerable,Ke=ze.splice,Xe=Pe?Pe.isConcatSpreadable:i,tt=Pe?Pe.iterator:i,rt=Pe?Pe.toStringTag:i,lt=function(){try{var e=pi(Te,"defineProperty");return e({},"",{}),e}catch(e){}}(),vt=t.clearTimeout!==dt.clearTimeout&&t.clearTimeout,pt=o&&o.now!==dt.Date.now&&o.now,ht=t.setTimeout!==dt.setTimeout&&t.setTimeout,jt=Be.ceil,yt=Be.floor,gt=Te.getOwnPropertySymbols,qt=We?We.isBuffer:i,Yt=t.isFinite,mn=ze.join,yn=ln(Te.keys,Te),gn=Be.max,bn=Be.min,wn=o.now,_n=t.parseInt,xn=Be.random,An=ze.reverse,kn=pi(t,"DataView"),Sn=pi(t,"Map"),Bn=pi(t,"Promise"),Tn=pi(t,"Set"),On=pi(t,"WeakMap"),Cn=pi(Te,"create"),Rn=On&&new On,zn={},En=Di(kn),Fn=Di(Sn),Ln=Di(Bn),In=Di(Tn),Jn=Di(On),Un=Pe?Pe.prototype:i,qn=Un?Un.valueOf:i,Dn=Un?Un.toString:i;function Mn(e){if(ra(e)&&!Hs(e)&&!(e instanceof Pn)){if(e instanceof Wn)return e;if(Je.call(e,"__wrapped__"))return Mi(e)}return new Wn(e)}var Nn=function(){function e(){}return function(t){if(!na(t))return{};if(Ge)return Ge(t);e.prototype=t;var n=new e;return e.prototype=i,n}}();function Vn(){}function Wn(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=i}function Pn(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=m,this.__views__=[]}function Hn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Yn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Qn(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Gn(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Qn;++t<n;)this.add(e[t])}function Zn(e){var t=this.__data__=new Yn(e);this.size=t.size}function Kn(e,t){var n=Hs(e),r=!n&&Ps(e),o=!n&&!r&&Zs(e),i=!n&&!r&&!o&&fa(e),s=n||r||o||i,a=s?Zt(e.length,Ce):[],u=a.length;for(var l in e)!t&&!Je.call(e,l)||s&&("length"==l||o&&("offset"==l||"parent"==l)||i&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||bi(l,u))||a.push(l);return a}function Xn(e){var t=e.length;return t?e[Gr(0,t-1)]:i}function $n(e,t){return Ji(Ro(e),ur(t,0,e.length))}function er(e){return Ji(Ro(e))}function tr(e,t,n){(n!==i&&!Ns(e[t],n)||n===i&&!(t in e))&&sr(e,t,n)}function nr(e,t,n){var r=e[t];Je.call(e,t)&&Ns(r,n)&&(n!==i||t in e)||sr(e,t,n)}function rr(e,t){for(var n=e.length;n--;)if(Ns(e[n][0],t))return n;return-1}function or(e,t,n,r){return pr(e,(function(e,o,i){t(r,e,n(e),i)})),r}function ir(e,t){return e&&zo(t,Ea(t),e)}function sr(e,t,n){"__proto__"==t&&lt?lt(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function ar(e,t){for(var n=-1,o=t.length,s=r(o),a=null==e;++n<o;)s[n]=a?i:Ta(e,t[n]);return s}function ur(e,t,n){return e==e&&(n!==i&&(e=e<=n?e:n),t!==i&&(e=e>=t?e:t)),e}function lr(e,t,n,r,o,s){var a,u=1&t,l=2&t,c=4&t;if(n&&(a=o?n(e,r,o,s):n(e)),a!==i)return a;if(!na(e))return e;var f=Hs(e);if(f){if(a=function(e){var t=e.length,n=new e.constructor(t);t&&"string"==typeof e[0]&&Je.call(e,"index")&&(n.index=e.index,n.input=e.input);return n}(e),!u)return Ro(e,a)}else{var v=ji(e),p=v==A||v==k;if(Zs(e))return ko(e,u);if(v==T||v==g||p&&!o){if(a=l||p?{}:yi(e),!u)return l?function(e,t){return zo(e,hi(e),t)}(e,function(e,t){return e&&zo(t,Fa(t),e)}(a,e)):function(e,t){return zo(e,di(e),t)}(e,ir(a,e))}else{if(!ut[v])return o?e:{};a=function(e,t,n){var r=e.constructor;switch(t){case L:return So(e);case w:case _:return new r(+e);case I:return function(e,t){var n=t?So(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}(e,n);case J:case U:case q:case D:case M:case N:case V:case W:case P:return Bo(e,n);case S:return new r;case B:case z:return new r(e);case C:return function(e){var t=new e.constructor(e.source,je.exec(e));return t.lastIndex=e.lastIndex,t}(e);case R:return new r;case E:return o=e,qn?Te(qn.call(o)):{}}var o}(e,v,u)}}s||(s=new Zn);var d=s.get(e);if(d)return d;if(s.set(e,a),ua(e))return e.forEach((function(r){a.add(lr(r,t,n,r,e,s))})),a;if(oa(e))return e.forEach((function(r,o){a.set(o,lr(r,t,n,o,e,s))})),a;var h=f?i:(c?l?si:ii:l?Fa:Ea)(e);return Tt(h||e,(function(r,o){h&&(r=e[o=r]),nr(a,o,lr(r,t,n,o,e,s))})),a}function cr(e,t,n){var r=n.length;if(null==e)return!r;for(e=Te(e);r--;){var o=n[r],s=t[o],a=e[o];if(a===i&&!(o in e)||!s(a))return!1}return!0}function fr(e,t,n){if("function"!=typeof e)throw new Re(s);return Ei((function(){e.apply(i,n)}),t)}function vr(e,t,n,r){var o=-1,i=zt,s=!0,a=e.length,u=[],l=t.length;if(!a)return u;n&&(t=Ft(t,Kt(n))),r?(i=Et,s=!1):t.length>=200&&(i=$t,s=!1,t=new Gn(t));e:for(;++o<a;){var c=e[o],f=null==n?c:n(c);if(c=r||0!==c?c:0,s&&f==f){for(var v=l;v--;)if(t[v]===f)continue e;u.push(c)}else i(t,f,r)||u.push(c)}return u}Mn.templateSettings={escape:$,evaluate:ee,interpolate:te,variable:"",imports:{_:Mn}},Mn.prototype=Vn.prototype,Mn.prototype.constructor=Mn,Wn.prototype=Nn(Vn.prototype),Wn.prototype.constructor=Wn,Pn.prototype=Nn(Vn.prototype),Pn.prototype.constructor=Pn,Hn.prototype.clear=function(){this.__data__=Cn?Cn(null):{},this.size=0},Hn.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Hn.prototype.get=function(e){var t=this.__data__;if(Cn){var n=t[e];return n===a?i:n}return Je.call(t,e)?t[e]:i},Hn.prototype.has=function(e){var t=this.__data__;return Cn?t[e]!==i:Je.call(t,e)},Hn.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Cn&&t===i?a:t,this},Yn.prototype.clear=function(){this.__data__=[],this.size=0},Yn.prototype.delete=function(e){var t=this.__data__,n=rr(t,e);return!(n<0)&&(n==t.length-1?t.pop():Ke.call(t,n,1),--this.size,!0)},Yn.prototype.get=function(e){var t=this.__data__,n=rr(t,e);return n<0?i:t[n][1]},Yn.prototype.has=function(e){return rr(this.__data__,e)>-1},Yn.prototype.set=function(e,t){var n=this.__data__,r=rr(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Qn.prototype.clear=function(){this.size=0,this.__data__={hash:new Hn,map:new(Sn||Yn),string:new Hn}},Qn.prototype.delete=function(e){var t=fi(this,e).delete(e);return this.size-=t?1:0,t},Qn.prototype.get=function(e){return fi(this,e).get(e)},Qn.prototype.has=function(e){return fi(this,e).has(e)},Qn.prototype.set=function(e,t){var n=fi(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},Gn.prototype.add=Gn.prototype.push=function(e){return this.__data__.set(e,a),this},Gn.prototype.has=function(e){return this.__data__.has(e)},Zn.prototype.clear=function(){this.__data__=new Yn,this.size=0},Zn.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Zn.prototype.get=function(e){return this.__data__.get(e)},Zn.prototype.has=function(e){return this.__data__.has(e)},Zn.prototype.set=function(e,t){var n=this.__data__;if(n instanceof Yn){var r=n.__data__;if(!Sn||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Qn(r)}return n.set(e,t),this.size=n.size,this};var pr=Lo(wr),dr=Lo(_r,!0);function hr(e,t){var n=!0;return pr(e,(function(e,r,o){return n=!!t(e,r,o)})),n}function jr(e,t,n){for(var r=-1,o=e.length;++r<o;){var s=e[r],a=t(s);if(null!=a&&(u===i?a==a&&!ca(a):n(a,u)))var u=a,l=s}return l}function mr(e,t){var n=[];return pr(e,(function(e,r,o){t(e,r,o)&&n.push(e)})),n}function yr(e,t,n,r,o){var i=-1,s=e.length;for(n||(n=gi),o||(o=[]);++i<s;){var a=e[i];t>0&&n(a)?t>1?yr(a,t-1,n,r,o):Lt(o,a):r||(o[o.length]=a)}return o}var gr=Io(),br=Io(!0);function wr(e,t){return e&&gr(e,t,Ea)}function _r(e,t){return e&&br(e,t,Ea)}function xr(e,t){return Rt(t,(function(t){return $s(e[t])}))}function Ar(e,t){for(var n=0,r=(t=wo(t,e)).length;null!=e&&n<r;)e=e[qi(t[n++])];return n&&n==r?e:i}function kr(e,t,n){var r=t(e);return Hs(e)?r:Lt(r,n(e))}function Sr(e){return null==e?e===i?"[object Undefined]":"[object Null]":rt&&rt in Te(e)?function(e){var t=Je.call(e,rt),n=e[rt];try{e[rt]=i;var r=!0}catch(e){}var o=De.call(e);r&&(t?e[rt]=n:delete e[rt]);return o}(e):function(e){return De.call(e)}(e)}function Br(e,t){return e>t}function Tr(e,t){return null!=e&&Je.call(e,t)}function Or(e,t){return null!=e&&t in Te(e)}function Cr(e,t,n){for(var o=n?Et:zt,s=e[0].length,a=e.length,u=a,l=r(a),c=1/0,f=[];u--;){var v=e[u];u&&t&&(v=Ft(v,Kt(t))),c=bn(v.length,c),l[u]=!n&&(t||s>=120&&v.length>=120)?new Gn(u&&v):i}v=e[0];var p=-1,d=l[0];e:for(;++p<s&&f.length<c;){var h=v[p],j=t?t(h):h;if(h=n||0!==h?h:0,!(d?$t(d,j):o(f,j,n))){for(u=a;--u;){var m=l[u];if(!(m?$t(m,j):o(e[u],j,n)))continue e}d&&d.push(j),f.push(h)}}return f}function Rr(e,t,n){var r=null==(e=Oi(e,t=wo(t,e)))?e:e[qi(Xi(t))];return null==r?i:St(r,e,n)}function zr(e){return ra(e)&&Sr(e)==g}function Er(e,t,n,r,o){return e===t||(null==e||null==t||!ra(e)&&!ra(t)?e!=e&&t!=t:function(e,t,n,r,o,s){var a=Hs(e),u=Hs(t),l=a?b:ji(e),c=u?b:ji(t),f=(l=l==g?T:l)==T,v=(c=c==g?T:c)==T,p=l==c;if(p&&Zs(e)){if(!Zs(t))return!1;a=!0,f=!1}if(p&&!f)return s||(s=new Zn),a||fa(e)?ri(e,t,n,r,o,s):function(e,t,n,r,o,i,s){switch(n){case I:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case L:return!(e.byteLength!=t.byteLength||!i(new He(e),new He(t)));case w:case _:case B:return Ns(+e,+t);case x:return e.name==t.name&&e.message==t.message;case C:case z:return e==t+"";case S:var a=un;case R:var u=1&r;if(a||(a=fn),e.size!=t.size&&!u)return!1;var l=s.get(e);if(l)return l==t;r|=2,s.set(e,t);var c=ri(a(e),a(t),r,o,i,s);return s.delete(e),c;case E:if(qn)return qn.call(e)==qn.call(t)}return!1}(e,t,l,n,r,o,s);if(!(1&n)){var d=f&&Je.call(e,"__wrapped__"),h=v&&Je.call(t,"__wrapped__");if(d||h){var j=d?e.value():e,m=h?t.value():t;return s||(s=new Zn),o(j,m,n,r,s)}}if(!p)return!1;return s||(s=new Zn),function(e,t,n,r,o,s){var a=1&n,u=ii(e),l=u.length,c=ii(t).length;if(l!=c&&!a)return!1;var f=l;for(;f--;){var v=u[f];if(!(a?v in t:Je.call(t,v)))return!1}var p=s.get(e);if(p&&s.get(t))return p==t;var d=!0;s.set(e,t),s.set(t,e);var h=a;for(;++f<l;){var j=e[v=u[f]],m=t[v];if(r)var y=a?r(m,j,v,t,e,s):r(j,m,v,e,t,s);if(!(y===i?j===m||o(j,m,n,r,s):y)){d=!1;break}h||(h="constructor"==v)}if(d&&!h){var g=e.constructor,b=t.constructor;g==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof g&&g instanceof g&&"function"==typeof b&&b instanceof b||(d=!1)}return s.delete(e),s.delete(t),d}(e,t,n,r,o,s)}(e,t,n,r,Er,o))}function Fr(e,t,n,r){var o=n.length,s=o,a=!r;if(null==e)return!s;for(e=Te(e);o--;){var u=n[o];if(a&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++o<s;){var l=(u=n[o])[0],c=e[l],f=u[1];if(a&&u[2]){if(c===i&&!(l in e))return!1}else{var v=new Zn;if(r)var p=r(c,f,l,e,t,v);if(!(p===i?Er(f,c,3,r,v):p))return!1}}return!0}function Lr(e){return!(!na(e)||(t=e,qe&&qe in t))&&($s(e)?Ve:ge).test(Di(e));var t}function Ir(e){return"function"==typeof e?e:null==e?iu:"object"==typeof e?Hs(e)?Nr(e[0],e[1]):Mr(e):du(e)}function Jr(e){if(!ki(e))return yn(e);var t=[];for(var n in Te(e))Je.call(e,n)&&"constructor"!=n&&t.push(n);return t}function Ur(e){if(!na(e))return function(e){var t=[];if(null!=e)for(var n in Te(e))t.push(n);return t}(e);var t=ki(e),n=[];for(var r in e)("constructor"!=r||!t&&Je.call(e,r))&&n.push(r);return n}function qr(e,t){return e<t}function Dr(e,t){var n=-1,o=Qs(e)?r(e.length):[];return pr(e,(function(e,r,i){o[++n]=t(e,r,i)})),o}function Mr(e){var t=vi(e);return 1==t.length&&t[0][2]?Bi(t[0][0],t[0][1]):function(n){return n===e||Fr(n,e,t)}}function Nr(e,t){return _i(e)&&Si(t)?Bi(qi(e),t):function(n){var r=Ta(n,e);return r===i&&r===t?Oa(n,e):Er(t,r,3)}}function Vr(e,t,n,r,o){e!==t&&gr(t,(function(s,a){if(na(s))o||(o=new Zn),function(e,t,n,r,o,s,a){var u=Ri(e,n),l=Ri(t,n),c=a.get(l);if(c)return void tr(e,n,c);var f=s?s(u,l,n+"",e,t,a):i,v=f===i;if(v){var p=Hs(l),d=!p&&Zs(l),h=!p&&!d&&fa(l);f=l,p||d||h?Hs(u)?f=u:Gs(u)?f=Ro(u):d?(v=!1,f=ko(l,!0)):h?(v=!1,f=Bo(l,!0)):f=[]:sa(l)||Ps(l)?(f=u,Ps(u)?f=ga(u):na(u)&&!$s(u)||(f=yi(l))):v=!1}v&&(a.set(l,f),o(f,l,r,s,a),a.delete(l));tr(e,n,f)}(e,t,a,n,Vr,r,o);else{var u=r?r(Ri(e,a),s,a+"",e,t,o):i;u===i&&(u=s),tr(e,a,u)}}),Fa)}function Wr(e,t){var n=e.length;if(n)return bi(t+=t<0?n:0,n)?e[t]:i}function Pr(e,t,n){var r=-1;return t=Ft(t.length?t:[iu],Kt(ci())),function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}(Dr(e,(function(e,n,o){return{criteria:Ft(t,(function(t){return t(e)})),index:++r,value:e}})),(function(e,t){return function(e,t,n){var r=-1,o=e.criteria,i=t.criteria,s=o.length,a=n.length;for(;++r<s;){var u=To(o[r],i[r]);if(u)return r>=a?u:u*("desc"==n[r]?-1:1)}return e.index-t.index}(e,t,n)}))}function Hr(e,t,n){for(var r=-1,o=t.length,i={};++r<o;){var s=t[r],a=Ar(e,s);n(a,s)&&eo(i,wo(s,e),a)}return i}function Yr(e,t,n,r){var o=r?Vt:Nt,i=-1,s=t.length,a=e;for(e===t&&(t=Ro(t)),n&&(a=Ft(e,Kt(n)));++i<s;)for(var u=0,l=t[i],c=n?n(l):l;(u=o(a,c,u,r))>-1;)a!==e&&Ke.call(a,u,1),Ke.call(e,u,1);return e}function Qr(e,t){for(var n=e?t.length:0,r=n-1;n--;){var o=t[n];if(n==r||o!==i){var i=o;bi(o)?Ke.call(e,o,1):vo(e,o)}}return e}function Gr(e,t){return e+yt(xn()*(t-e+1))}function Zr(e,t){var n="";if(!e||t<1||t>h)return n;do{t%2&&(n+=e),(t=yt(t/2))&&(e+=e)}while(t);return n}function Kr(e,t){return Fi(Ti(e,t,iu),e+"")}function Xr(e){return Xn(Na(e))}function $r(e,t){var n=Na(e);return Ji(n,ur(t,0,n.length))}function eo(e,t,n,r){if(!na(e))return e;for(var o=-1,s=(t=wo(t,e)).length,a=s-1,u=e;null!=u&&++o<s;){var l=qi(t[o]),c=n;if(o!=a){var f=u[l];(c=r?r(f,l,u):i)===i&&(c=na(f)?f:bi(t[o+1])?[]:{})}nr(u,l,c),u=u[l]}return e}var to=Rn?function(e,t){return Rn.set(e,t),e}:iu,no=lt?function(e,t){return lt(e,"toString",{configurable:!0,enumerable:!1,value:nu(t),writable:!0})}:iu;function ro(e){return Ji(Na(e))}function oo(e,t,n){var o=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var s=r(i);++o<i;)s[o]=e[o+t];return s}function io(e,t){var n;return pr(e,(function(e,r,o){return!(n=t(e,r,o))})),!!n}function so(e,t,n){var r=0,o=null==e?r:e.length;if("number"==typeof t&&t==t&&o<=2147483647){for(;r<o;){var i=r+o>>>1,s=e[i];null!==s&&!ca(s)&&(n?s<=t:s<t)?r=i+1:o=i}return o}return ao(e,t,iu,n)}function ao(e,t,n,r){t=n(t);for(var o=0,s=null==e?0:e.length,a=t!=t,u=null===t,l=ca(t),c=t===i;o<s;){var f=yt((o+s)/2),v=n(e[f]),p=v!==i,d=null===v,h=v==v,j=ca(v);if(a)var m=r||h;else m=c?h&&(r||p):u?h&&p&&(r||!d):l?h&&p&&!d&&(r||!j):!d&&!j&&(r?v<=t:v<t);m?o=f+1:s=f}return bn(s,4294967294)}function uo(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var s=e[n],a=t?t(s):s;if(!n||!Ns(a,u)){var u=a;i[o++]=0===s?0:s}}return i}function lo(e){return"number"==typeof e?e:ca(e)?j:+e}function co(e){if("string"==typeof e)return e;if(Hs(e))return Ft(e,co)+"";if(ca(e))return Dn?Dn.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function fo(e,t,n){var r=-1,o=zt,i=e.length,s=!0,a=[],u=a;if(n)s=!1,o=Et;else if(i>=200){var l=t?null:Ko(e);if(l)return fn(l);s=!1,o=$t,u=new Gn}else u=t?[]:a;e:for(;++r<i;){var c=e[r],f=t?t(c):c;if(c=n||0!==c?c:0,s&&f==f){for(var v=u.length;v--;)if(u[v]===f)continue e;t&&u.push(f),a.push(c)}else o(u,f,n)||(u!==a&&u.push(f),a.push(c))}return a}function vo(e,t){return null==(e=Oi(e,t=wo(t,e)))||delete e[qi(Xi(t))]}function po(e,t,n,r){return eo(e,t,n(Ar(e,t)),r)}function ho(e,t,n,r){for(var o=e.length,i=r?o:-1;(r?i--:++i<o)&&t(e[i],i,e););return n?oo(e,r?0:i,r?i+1:o):oo(e,r?i+1:0,r?o:i)}function jo(e,t){var n=e;return n instanceof Pn&&(n=n.value()),It(t,(function(e,t){return t.func.apply(t.thisArg,Lt([e],t.args))}),n)}function mo(e,t,n){var o=e.length;if(o<2)return o?fo(e[0]):[];for(var i=-1,s=r(o);++i<o;)for(var a=e[i],u=-1;++u<o;)u!=i&&(s[i]=vr(s[i]||a,e[u],t,n));return fo(yr(s,1),t,n)}function yo(e,t,n){for(var r=-1,o=e.length,s=t.length,a={};++r<o;){var u=r<s?t[r]:i;n(a,e[r],u)}return a}function go(e){return Gs(e)?e:[]}function bo(e){return"function"==typeof e?e:iu}function wo(e,t){return Hs(e)?e:_i(e,t)?[e]:Ui(ba(e))}var _o=Kr;function xo(e,t,n){var r=e.length;return n=n===i?r:n,!t&&n>=r?e:oo(e,t,n)}var Ao=vt||function(e){return dt.clearTimeout(e)};function ko(e,t){if(t)return e.slice();var n=e.length,r=Ye?Ye(n):new e.constructor(n);return e.copy(r),r}function So(e){var t=new e.constructor(e.byteLength);return new He(t).set(new He(e)),t}function Bo(e,t){var n=t?So(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function To(e,t){if(e!==t){var n=e!==i,r=null===e,o=e==e,s=ca(e),a=t!==i,u=null===t,l=t==t,c=ca(t);if(!u&&!c&&!s&&e>t||s&&a&&l&&!u&&!c||r&&a&&l||!n&&l||!o)return 1;if(!r&&!s&&!c&&e<t||c&&n&&o&&!r&&!s||u&&n&&o||!a&&o||!l)return-1}return 0}function Oo(e,t,n,o){for(var i=-1,s=e.length,a=n.length,u=-1,l=t.length,c=gn(s-a,0),f=r(l+c),v=!o;++u<l;)f[u]=t[u];for(;++i<a;)(v||i<s)&&(f[n[i]]=e[i]);for(;c--;)f[u++]=e[i++];return f}function Co(e,t,n,o){for(var i=-1,s=e.length,a=-1,u=n.length,l=-1,c=t.length,f=gn(s-u,0),v=r(f+c),p=!o;++i<f;)v[i]=e[i];for(var d=i;++l<c;)v[d+l]=t[l];for(;++a<u;)(p||i<s)&&(v[d+n[a]]=e[i++]);return v}function Ro(e,t){var n=-1,o=e.length;for(t||(t=r(o));++n<o;)t[n]=e[n];return t}function zo(e,t,n,r){var o=!n;n||(n={});for(var s=-1,a=t.length;++s<a;){var u=t[s],l=r?r(n[u],e[u],u,n,e):i;l===i&&(l=e[u]),o?sr(n,u,l):nr(n,u,l)}return n}function Eo(e,t){return function(n,r){var o=Hs(n)?Bt:or,i=t?t():{};return o(n,e,ci(r,2),i)}}function Fo(e){return Kr((function(t,n){var r=-1,o=n.length,s=o>1?n[o-1]:i,a=o>2?n[2]:i;for(s=e.length>3&&"function"==typeof s?(o--,s):i,a&&wi(n[0],n[1],a)&&(s=o<3?i:s,o=1),t=Te(t);++r<o;){var u=n[r];u&&e(t,u,r,s)}return t}))}function Lo(e,t){return function(n,r){if(null==n)return n;if(!Qs(n))return e(n,r);for(var o=n.length,i=t?o:-1,s=Te(n);(t?i--:++i<o)&&!1!==r(s[i],i,s););return n}}function Io(e){return function(t,n,r){for(var o=-1,i=Te(t),s=r(t),a=s.length;a--;){var u=s[e?a:++o];if(!1===n(i[u],u,i))break}return t}}function Jo(e){return function(t){var n=an(t=ba(t))?dn(t):i,r=n?n[0]:t.charAt(0),o=n?xo(n,1).join(""):t.slice(1);return r[e]()+o}}function Uo(e){return function(t){return It($a(Pa(t).replace($e,"")),e,"")}}function qo(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=Nn(e.prototype),r=e.apply(n,t);return na(r)?r:n}}function Do(e){return function(t,n,r){var o=Te(t);if(!Qs(t)){var s=ci(n,3);t=Ea(t),n=function(e){return s(o[e],e,o)}}var a=e(t,n,r);return a>-1?o[s?t[a]:a]:i}}function Mo(e){return oi((function(t){var n=t.length,r=n,o=Wn.prototype.thru;for(e&&t.reverse();r--;){var a=t[r];if("function"!=typeof a)throw new Re(s);if(o&&!u&&"wrapper"==ui(a))var u=new Wn([],!0)}for(r=u?r:n;++r<n;){var l=ui(a=t[r]),c="wrapper"==l?ai(a):i;u=c&&xi(c[0])&&424==c[1]&&!c[4].length&&1==c[9]?u[ui(c[0])].apply(u,c[3]):1==a.length&&xi(a)?u[l]():u.thru(a)}return function(){var e=arguments,r=e[0];if(u&&1==e.length&&Hs(r))return u.plant(r).value();for(var o=0,i=n?t[o].apply(this,e):r;++o<n;)i=t[o].call(this,i);return i}}))}function No(e,t,n,o,s,a,u,l,c,f){var p=t&v,d=1&t,h=2&t,j=24&t,m=512&t,y=h?i:qo(e);return function i(){for(var v=arguments.length,g=r(v),b=v;b--;)g[b]=arguments[b];if(j)var w=li(i),_=nn(g,w);if(o&&(g=Oo(g,o,s,j)),a&&(g=Co(g,a,u,j)),v-=_,j&&v<f){var x=cn(g,w);return Go(e,t,No,i.placeholder,n,g,x,l,c,f-v)}var A=d?n:this,k=h?A[e]:e;return v=g.length,l?g=Ci(g,l):m&&v>1&&g.reverse(),p&&c<v&&(g.length=c),this&&this!==dt&&this instanceof i&&(k=y||qo(k)),k.apply(A,g)}}function Vo(e,t){return function(n,r){return function(e,t,n,r){return wr(e,(function(e,o,i){t(r,n(e),o,i)})),r}(n,e,t(r),{})}}function Wo(e,t){return function(n,r){var o;if(n===i&&r===i)return t;if(n!==i&&(o=n),r!==i){if(o===i)return r;"string"==typeof n||"string"==typeof r?(n=co(n),r=co(r)):(n=lo(n),r=lo(r)),o=e(n,r)}return o}}function Po(e){return oi((function(t){return t=Ft(t,Kt(ci())),Kr((function(n){var r=this;return e(t,(function(e){return St(e,r,n)}))}))}))}function Ho(e,t){var n=(t=t===i?" ":co(t)).length;if(n<2)return n?Zr(t,e):t;var r=Zr(t,jt(e/pn(t)));return an(t)?xo(dn(r),0,e).join(""):r.slice(0,e)}function Yo(e){return function(t,n,o){return o&&"number"!=typeof o&&wi(t,n,o)&&(n=o=i),t=ha(t),n===i?(n=t,t=0):n=ha(n),function(e,t,n,o){for(var i=-1,s=gn(jt((t-e)/(n||1)),0),a=r(s);s--;)a[o?s:++i]=e,e+=n;return a}(t,n,o=o===i?t<n?1:-1:ha(o),e)}}function Qo(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=ya(t),n=ya(n)),e(t,n)}}function Go(e,t,n,r,o,s,a,u,l,v){var p=8&t;t|=p?c:f,4&(t&=~(p?f:c))||(t&=-4);var d=[e,t,o,p?s:i,p?a:i,p?i:s,p?i:a,u,l,v],h=n.apply(i,d);return xi(e)&&zi(h,d),h.placeholder=r,Li(h,e,t)}function Zo(e){var t=Be[e];return function(e,n){if(e=ya(e),n=null==n?0:bn(ja(n),292)){var r=(ba(e)+"e").split("e");return+((r=(ba(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var Ko=Tn&&1/fn(new Tn([,-0]))[1]==d?function(e){return new Tn(e)}:cu;function Xo(e){return function(t){var n=ji(t);return n==S?un(t):n==R?vn(t):function(e,t){return Ft(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function $o(e,t,n,o,a,d,h,j){var m=2&t;if(!m&&"function"!=typeof e)throw new Re(s);var y=o?o.length:0;if(y||(t&=-97,o=a=i),h=h===i?h:gn(ja(h),0),j=j===i?j:ja(j),y-=a?a.length:0,t&f){var g=o,b=a;o=a=i}var w=m?i:ai(e),_=[e,t,n,o,a,g,b,d,h,j];if(w&&function(e,t){var n=e[1],r=t[1],o=n|r,i=o<131,s=r==v&&8==n||r==v&&n==p&&e[7].length<=t[8]||384==r&&t[7].length<=t[8]&&8==n;if(!i&&!s)return e;1&r&&(e[2]=t[2],o|=1&n?0:4);var a=t[3];if(a){var l=e[3];e[3]=l?Oo(l,a,t[4]):a,e[4]=l?cn(e[3],u):t[4]}(a=t[5])&&(l=e[5],e[5]=l?Co(l,a,t[6]):a,e[6]=l?cn(e[5],u):t[6]);(a=t[7])&&(e[7]=a);r&v&&(e[8]=null==e[8]?t[8]:bn(e[8],t[8]));null==e[9]&&(e[9]=t[9]);e[0]=t[0],e[1]=o}(_,w),e=_[0],t=_[1],n=_[2],o=_[3],a=_[4],!(j=_[9]=_[9]===i?m?0:e.length:gn(_[9]-y,0))&&24&t&&(t&=-25),t&&1!=t)x=8==t||t==l?function(e,t,n){var o=qo(e);return function s(){for(var a=arguments.length,u=r(a),l=a,c=li(s);l--;)u[l]=arguments[l];var f=a<3&&u[0]!==c&&u[a-1]!==c?[]:cn(u,c);return(a-=f.length)<n?Go(e,t,No,s.placeholder,i,u,f,i,i,n-a):St(this&&this!==dt&&this instanceof s?o:e,this,u)}}(e,t,j):t!=c&&33!=t||a.length?No.apply(i,_):function(e,t,n,o){var i=1&t,s=qo(e);return function t(){for(var a=-1,u=arguments.length,l=-1,c=o.length,f=r(c+u),v=this&&this!==dt&&this instanceof t?s:e;++l<c;)f[l]=o[l];for(;u--;)f[l++]=arguments[++a];return St(v,i?n:this,f)}}(e,t,n,o);else var x=function(e,t,n){var r=1&t,o=qo(e);return function t(){return(this&&this!==dt&&this instanceof t?o:e).apply(r?n:this,arguments)}}(e,t,n);return Li((w?to:zi)(x,_),e,t)}function ei(e,t,n,r){return e===i||Ns(e,Fe[n])&&!Je.call(r,n)?t:e}function ti(e,t,n,r,o,s){return na(e)&&na(t)&&(s.set(t,e),Vr(e,t,i,ti,s),s.delete(t)),e}function ni(e){return sa(e)?i:e}function ri(e,t,n,r,o,s){var a=1&n,u=e.length,l=t.length;if(u!=l&&!(a&&l>u))return!1;var c=s.get(e);if(c&&s.get(t))return c==t;var f=-1,v=!0,p=2&n?new Gn:i;for(s.set(e,t),s.set(t,e);++f<u;){var d=e[f],h=t[f];if(r)var j=a?r(h,d,f,t,e,s):r(d,h,f,e,t,s);if(j!==i){if(j)continue;v=!1;break}if(p){if(!Ut(t,(function(e,t){if(!$t(p,t)&&(d===e||o(d,e,n,r,s)))return p.push(t)}))){v=!1;break}}else if(d!==h&&!o(d,h,n,r,s)){v=!1;break}}return s.delete(e),s.delete(t),v}function oi(e){return Fi(Ti(e,i,Yi),e+"")}function ii(e){return kr(e,Ea,di)}function si(e){return kr(e,Fa,hi)}var ai=Rn?function(e){return Rn.get(e)}:cu;function ui(e){for(var t=e.name+"",n=zn[t],r=Je.call(zn,t)?n.length:0;r--;){var o=n[r],i=o.func;if(null==i||i==e)return o.name}return t}function li(e){return(Je.call(Mn,"placeholder")?Mn:e).placeholder}function ci(){var e=Mn.iteratee||su;return e=e===su?Ir:e,arguments.length?e(arguments[0],arguments[1]):e}function fi(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map}function vi(e){for(var t=Ea(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,Si(o)]}return t}function pi(e,t){var n=function(e,t){return null==e?i:e[t]}(e,t);return Lr(n)?n:i}var di=gt?function(e){return null==e?[]:(e=Te(e),Rt(gt(e),(function(t){return Ze.call(e,t)})))}:mu,hi=gt?function(e){for(var t=[];e;)Lt(t,di(e)),e=Qe(e);return t}:mu,ji=Sr;function mi(e,t,n){for(var r=-1,o=(t=wo(t,e)).length,i=!1;++r<o;){var s=qi(t[r]);if(!(i=null!=e&&n(e,s)))break;e=e[s]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&ta(o)&&bi(s,o)&&(Hs(e)||Ps(e))}function yi(e){return"function"!=typeof e.constructor||ki(e)?{}:Nn(Qe(e))}function gi(e){return Hs(e)||Ps(e)||!!(Xe&&e&&e[Xe])}function bi(e,t){var n=typeof e;return!!(t=null==t?h:t)&&("number"==n||"symbol"!=n&&we.test(e))&&e>-1&&e%1==0&&e<t}function wi(e,t,n){if(!na(n))return!1;var r=typeof t;return!!("number"==r?Qs(n)&&bi(t,n.length):"string"==r&&t in n)&&Ns(n[t],e)}function _i(e,t){if(Hs(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!ca(e))||(re.test(e)||!ne.test(e)||null!=t&&e in Te(t))}function xi(e){var t=ui(e),n=Mn[t];if("function"!=typeof n||!(t in Pn.prototype))return!1;if(e===n)return!0;var r=ai(n);return!!r&&e===r[0]}(kn&&ji(new kn(new ArrayBuffer(1)))!=I||Sn&&ji(new Sn)!=S||Bn&&ji(Bn.resolve())!=O||Tn&&ji(new Tn)!=R||On&&ji(new On)!=F)&&(ji=function(e){var t=Sr(e),n=t==T?e.constructor:i,r=n?Di(n):"";if(r)switch(r){case En:return I;case Fn:return S;case Ln:return O;case In:return R;case Jn:return F}return t});var Ai=Le?$s:yu;function ki(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Fe)}function Si(e){return e==e&&!na(e)}function Bi(e,t){return function(n){return null!=n&&(n[e]===t&&(t!==i||e in Te(n)))}}function Ti(e,t,n){return t=gn(t===i?e.length-1:t,0),function(){for(var o=arguments,i=-1,s=gn(o.length-t,0),a=r(s);++i<s;)a[i]=o[t+i];i=-1;for(var u=r(t+1);++i<t;)u[i]=o[i];return u[t]=n(a),St(e,this,u)}}function Oi(e,t){return t.length<2?e:Ar(e,oo(t,0,-1))}function Ci(e,t){for(var n=e.length,r=bn(t.length,n),o=Ro(e);r--;){var s=t[r];e[r]=bi(s,n)?o[s]:i}return e}function Ri(e,t){if("__proto__"!=t)return e[t]}var zi=Ii(to),Ei=ht||function(e,t){return dt.setTimeout(e,t)},Fi=Ii(no);function Li(e,t,n){var r=t+"";return Fi(e,function(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(ce,"{\n/* [wrapped with "+t+"] */\n")}(r,function(e,t){return Tt(y,(function(n){var r="_."+n[0];t&n[1]&&!zt(e,r)&&e.push(r)})),e.sort()}(function(e){var t=e.match(fe);return t?t[1].split(ve):[]}(r),n)))}function Ii(e){var t=0,n=0;return function(){var r=wn(),o=16-(r-n);if(n=r,o>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(i,arguments)}}function Ji(e,t){var n=-1,r=e.length,o=r-1;for(t=t===i?r:t;++n<t;){var s=Gr(n,o),a=e[s];e[s]=e[n],e[n]=a}return e.length=t,e}var Ui=function(e){var t=Is(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(oe,(function(e,n,r,o){t.push(r?o.replace(de,"$1"):n||e)})),t}));function qi(e){if("string"==typeof e||ca(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Di(e){if(null!=e){try{return Ie.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Mi(e){if(e instanceof Pn)return e.clone();var t=new Wn(e.__wrapped__,e.__chain__);return t.__actions__=Ro(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var Ni=Kr((function(e,t){return Gs(e)?vr(e,yr(t,1,Gs,!0)):[]})),Vi=Kr((function(e,t){var n=Xi(t);return Gs(n)&&(n=i),Gs(e)?vr(e,yr(t,1,Gs,!0),ci(n,2)):[]})),Wi=Kr((function(e,t){var n=Xi(t);return Gs(n)&&(n=i),Gs(e)?vr(e,yr(t,1,Gs,!0),i,n):[]}));function Pi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:ja(n);return o<0&&(o=gn(r+o,0)),Mt(e,ci(t,3),o)}function Hi(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r-1;return n!==i&&(o=ja(n),o=n<0?gn(r+o,0):bn(o,r-1)),Mt(e,ci(t,3),o,!0)}function Yi(e){return(null==e?0:e.length)?yr(e,1):[]}function Qi(e){return e&&e.length?e[0]:i}var Gi=Kr((function(e){var t=Ft(e,go);return t.length&&t[0]===e[0]?Cr(t):[]})),Zi=Kr((function(e){var t=Xi(e),n=Ft(e,go);return t===Xi(n)?t=i:n.pop(),n.length&&n[0]===e[0]?Cr(n,ci(t,2)):[]})),Ki=Kr((function(e){var t=Xi(e),n=Ft(e,go);return(t="function"==typeof t?t:i)&&n.pop(),n.length&&n[0]===e[0]?Cr(n,i,t):[]}));function Xi(e){var t=null==e?0:e.length;return t?e[t-1]:i}var $i=Kr(es);function es(e,t){return e&&e.length&&t&&t.length?Yr(e,t):e}var ts=oi((function(e,t){var n=null==e?0:e.length,r=ar(e,t);return Qr(e,Ft(t,(function(e){return bi(e,n)?+e:e})).sort(To)),r}));function ns(e){return null==e?e:An.call(e)}var rs=Kr((function(e){return fo(yr(e,1,Gs,!0))})),os=Kr((function(e){var t=Xi(e);return Gs(t)&&(t=i),fo(yr(e,1,Gs,!0),ci(t,2))})),is=Kr((function(e){var t=Xi(e);return t="function"==typeof t?t:i,fo(yr(e,1,Gs,!0),i,t)}));function ss(e){if(!e||!e.length)return[];var t=0;return e=Rt(e,(function(e){if(Gs(e))return t=gn(e.length,t),!0})),Zt(t,(function(t){return Ft(e,Ht(t))}))}function as(e,t){if(!e||!e.length)return[];var n=ss(e);return null==t?n:Ft(n,(function(e){return St(t,i,e)}))}var us=Kr((function(e,t){return Gs(e)?vr(e,t):[]})),ls=Kr((function(e){return mo(Rt(e,Gs))})),cs=Kr((function(e){var t=Xi(e);return Gs(t)&&(t=i),mo(Rt(e,Gs),ci(t,2))})),fs=Kr((function(e){var t=Xi(e);return t="function"==typeof t?t:i,mo(Rt(e,Gs),i,t)})),vs=Kr(ss);var ps=Kr((function(e){var t=e.length,n=t>1?e[t-1]:i;return n="function"==typeof n?(e.pop(),n):i,as(e,n)}));function ds(e){var t=Mn(e);return t.__chain__=!0,t}function hs(e,t){return t(e)}var js=oi((function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,o=function(t){return ar(t,e)};return!(t>1||this.__actions__.length)&&r instanceof Pn&&bi(n)?((r=r.slice(n,+n+(t?1:0))).__actions__.push({func:hs,args:[o],thisArg:i}),new Wn(r,this.__chain__).thru((function(e){return t&&!e.length&&e.push(i),e}))):this.thru(o)}));var ms=Eo((function(e,t,n){Je.call(e,n)?++e[n]:sr(e,n,1)}));var ys=Do(Pi),gs=Do(Hi);function bs(e,t){return(Hs(e)?Tt:pr)(e,ci(t,3))}function ws(e,t){return(Hs(e)?Ot:dr)(e,ci(t,3))}var _s=Eo((function(e,t,n){Je.call(e,n)?e[n].push(t):sr(e,n,[t])}));var xs=Kr((function(e,t,n){var o=-1,i="function"==typeof t,s=Qs(e)?r(e.length):[];return pr(e,(function(e){s[++o]=i?St(t,e,n):Rr(e,t,n)})),s})),As=Eo((function(e,t,n){sr(e,n,t)}));function ks(e,t){return(Hs(e)?Ft:Dr)(e,ci(t,3))}var Ss=Eo((function(e,t,n){e[n?0:1].push(t)}),(function(){return[[],[]]}));var Bs=Kr((function(e,t){if(null==e)return[];var n=t.length;return n>1&&wi(e,t[0],t[1])?t=[]:n>2&&wi(t[0],t[1],t[2])&&(t=[t[0]]),Pr(e,yr(t,1),[])})),Ts=pt||function(){return dt.Date.now()};function Os(e,t,n){return t=n?i:t,t=e&&null==t?e.length:t,$o(e,v,i,i,i,i,t)}function Cs(e,t){var n;if("function"!=typeof t)throw new Re(s);return e=ja(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=i),n}}var Rs=Kr((function(e,t,n){var r=1;if(n.length){var o=cn(n,li(Rs));r|=c}return $o(e,r,t,n,o)})),zs=Kr((function(e,t,n){var r=3;if(n.length){var o=cn(n,li(zs));r|=c}return $o(t,r,e,n,o)}));function Es(e,t,n){var r,o,a,u,l,c,f=0,v=!1,p=!1,d=!0;if("function"!=typeof e)throw new Re(s);function h(t){var n=r,s=o;return r=o=i,f=t,u=e.apply(s,n)}function j(e){return f=e,l=Ei(y,t),v?h(e):u}function m(e){var n=e-c;return c===i||n>=t||n<0||p&&e-f>=a}function y(){var e=Ts();if(m(e))return g(e);l=Ei(y,function(e){var n=t-(e-c);return p?bn(n,a-(e-f)):n}(e))}function g(e){return l=i,d&&r?h(e):(r=o=i,u)}function b(){var e=Ts(),n=m(e);if(r=arguments,o=this,c=e,n){if(l===i)return j(c);if(p)return l=Ei(y,t),h(c)}return l===i&&(l=Ei(y,t)),u}return t=ya(t)||0,na(n)&&(v=!!n.leading,a=(p="maxWait"in n)?gn(ya(n.maxWait)||0,t):a,d="trailing"in n?!!n.trailing:d),b.cancel=function(){l!==i&&Ao(l),f=0,r=c=o=l=i},b.flush=function(){return l===i?u:g(Ts())},b}var Fs=Kr((function(e,t){return fr(e,1,t)})),Ls=Kr((function(e,t,n){return fr(e,ya(t)||0,n)}));function Is(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Re(s);var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var s=e.apply(this,r);return n.cache=i.set(o,s)||i,s};return n.cache=new(Is.Cache||Qn),n}function Js(e){if("function"!=typeof e)throw new Re(s);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Is.Cache=Qn;var Us=_o((function(e,t){var n=(t=1==t.length&&Hs(t[0])?Ft(t[0],Kt(ci())):Ft(yr(t,1),Kt(ci()))).length;return Kr((function(r){for(var o=-1,i=bn(r.length,n);++o<i;)r[o]=t[o].call(this,r[o]);return St(e,this,r)}))})),qs=Kr((function(e,t){var n=cn(t,li(qs));return $o(e,c,i,t,n)})),Ds=Kr((function(e,t){var n=cn(t,li(Ds));return $o(e,f,i,t,n)})),Ms=oi((function(e,t){return $o(e,p,i,i,i,t)}));function Ns(e,t){return e===t||e!=e&&t!=t}var Vs=Qo(Br),Ws=Qo((function(e,t){return e>=t})),Ps=zr(function(){return arguments}())?zr:function(e){return ra(e)&&Je.call(e,"callee")&&!Ze.call(e,"callee")},Hs=r.isArray,Ys=bt?Kt(bt):function(e){return ra(e)&&Sr(e)==L};function Qs(e){return null!=e&&ta(e.length)&&!$s(e)}function Gs(e){return ra(e)&&Qs(e)}var Zs=qt||yu,Ks=wt?Kt(wt):function(e){return ra(e)&&Sr(e)==_};function Xs(e){if(!ra(e))return!1;var t=Sr(e);return t==x||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!sa(e)}function $s(e){if(!na(e))return!1;var t=Sr(e);return t==A||t==k||"[object AsyncFunction]"==t||"[object Proxy]"==t}function ea(e){return"number"==typeof e&&e==ja(e)}function ta(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=h}function na(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function ra(e){return null!=e&&"object"==typeof e}var oa=_t?Kt(_t):function(e){return ra(e)&&ji(e)==S};function ia(e){return"number"==typeof e||ra(e)&&Sr(e)==B}function sa(e){if(!ra(e)||Sr(e)!=T)return!1;var t=Qe(e);if(null===t)return!0;var n=Je.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Ie.call(n)==Me}var aa=xt?Kt(xt):function(e){return ra(e)&&Sr(e)==C};var ua=At?Kt(At):function(e){return ra(e)&&ji(e)==R};function la(e){return"string"==typeof e||!Hs(e)&&ra(e)&&Sr(e)==z}function ca(e){return"symbol"==typeof e||ra(e)&&Sr(e)==E}var fa=kt?Kt(kt):function(e){return ra(e)&&ta(e.length)&&!!at[Sr(e)]};var va=Qo(qr),pa=Qo((function(e,t){return e<=t}));function da(e){if(!e)return[];if(Qs(e))return la(e)?dn(e):Ro(e);if(tt&&e[tt])return function(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}(e[tt]());var t=ji(e);return(t==S?un:t==R?fn:Na)(e)}function ha(e){return e?(e=ya(e))===d||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function ja(e){var t=ha(e),n=t%1;return t==t?n?t-n:t:0}function ma(e){return e?ur(ja(e),0,m):0}function ya(e){if("number"==typeof e)return e;if(ca(e))return j;if(na(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=na(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(ae,"");var n=ye.test(e);return n||be.test(e)?ft(e.slice(2),n?2:8):me.test(e)?j:+e}function ga(e){return zo(e,Fa(e))}function ba(e){return null==e?"":co(e)}var wa=Fo((function(e,t){if(ki(t)||Qs(t))zo(t,Ea(t),e);else for(var n in t)Je.call(t,n)&&nr(e,n,t[n])})),_a=Fo((function(e,t){zo(t,Fa(t),e)})),xa=Fo((function(e,t,n,r){zo(t,Fa(t),e,r)})),Aa=Fo((function(e,t,n,r){zo(t,Ea(t),e,r)})),ka=oi(ar);var Sa=Kr((function(e,t){e=Te(e);var n=-1,r=t.length,o=r>2?t[2]:i;for(o&&wi(t[0],t[1],o)&&(r=1);++n<r;)for(var s=t[n],a=Fa(s),u=-1,l=a.length;++u<l;){var c=a[u],f=e[c];(f===i||Ns(f,Fe[c])&&!Je.call(e,c))&&(e[c]=s[c])}return e})),Ba=Kr((function(e){return e.push(i,ti),St(Ia,i,e)}));function Ta(e,t,n){var r=null==e?i:Ar(e,t);return r===i?n:r}function Oa(e,t){return null!=e&&mi(e,t,Or)}var Ca=Vo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=De.call(t)),e[t]=n}),nu(iu)),Ra=Vo((function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=De.call(t)),Je.call(e,t)?e[t].push(n):e[t]=[n]}),ci),za=Kr(Rr);function Ea(e){return Qs(e)?Kn(e):Jr(e)}function Fa(e){return Qs(e)?Kn(e,!0):Ur(e)}var La=Fo((function(e,t,n){Vr(e,t,n)})),Ia=Fo((function(e,t,n,r){Vr(e,t,n,r)})),Ja=oi((function(e,t){var n={};if(null==e)return n;var r=!1;t=Ft(t,(function(t){return t=wo(t,e),r||(r=t.length>1),t})),zo(e,si(e),n),r&&(n=lr(n,7,ni));for(var o=t.length;o--;)vo(n,t[o]);return n}));var Ua=oi((function(e,t){return null==e?{}:function(e,t){return Hr(e,t,(function(t,n){return Oa(e,n)}))}(e,t)}));function qa(e,t){if(null==e)return{};var n=Ft(si(e),(function(e){return[e]}));return t=ci(t),Hr(e,n,(function(e,n){return t(e,n[0])}))}var Da=Xo(Ea),Ma=Xo(Fa);function Na(e){return null==e?[]:Xt(e,Ea(e))}var Va=Uo((function(e,t,n){return t=t.toLowerCase(),e+(n?Wa(t):t)}));function Wa(e){return Xa(ba(e).toLowerCase())}function Pa(e){return(e=ba(e))&&e.replace(_e,rn).replace(et,"")}var Ha=Uo((function(e,t,n){return e+(n?"-":"")+t.toLowerCase()})),Ya=Uo((function(e,t,n){return e+(n?" ":"")+t.toLowerCase()})),Qa=Jo("toLowerCase");var Ga=Uo((function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}));var Za=Uo((function(e,t,n){return e+(n?" ":"")+Xa(t)}));var Ka=Uo((function(e,t,n){return e+(n?" ":"")+t.toUpperCase()})),Xa=Jo("toUpperCase");function $a(e,t,n){return e=ba(e),(t=n?i:t)===i?function(e){return ot.test(e)}(e)?function(e){return e.match(nt)||[]}(e):function(e){return e.match(pe)||[]}(e):e.match(t)||[]}var eu=Kr((function(e,t){try{return St(e,i,t)}catch(e){return Xs(e)?e:new ke(e)}})),tu=oi((function(e,t){return Tt(t,(function(t){t=qi(t),sr(e,t,Rs(e[t],e))})),e}));function nu(e){return function(){return e}}var ru=Mo(),ou=Mo(!0);function iu(e){return e}function su(e){return Ir("function"==typeof e?e:lr(e,1))}var au=Kr((function(e,t){return function(n){return Rr(n,e,t)}})),uu=Kr((function(e,t){return function(n){return Rr(e,n,t)}}));function lu(e,t,n){var r=Ea(t),o=xr(t,r);null!=n||na(t)&&(o.length||!r.length)||(n=t,t=e,e=this,o=xr(t,Ea(t)));var i=!(na(n)&&"chain"in n&&!n.chain),s=$s(e);return Tt(o,(function(n){var r=t[n];e[n]=r,s&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__),o=n.__actions__=Ro(this.__actions__);return o.push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,Lt([this.value()],arguments))})})),e}function cu(){}var fu=Po(Ft),vu=Po(Ct),pu=Po(Ut);function du(e){return _i(e)?Ht(qi(e)):function(e){return function(t){return Ar(t,e)}}(e)}var hu=Yo(),ju=Yo(!0);function mu(){return[]}function yu(){return!1}var gu=Wo((function(e,t){return e+t}),0),bu=Zo("ceil"),wu=Wo((function(e,t){return e/t}),1),_u=Zo("floor");var xu,Au=Wo((function(e,t){return e*t}),1),ku=Zo("round"),Su=Wo((function(e,t){return e-t}),0);return Mn.after=function(e,t){if("function"!=typeof t)throw new Re(s);return e=ja(e),function(){if(--e<1)return t.apply(this,arguments)}},Mn.ary=Os,Mn.assign=wa,Mn.assignIn=_a,Mn.assignInWith=xa,Mn.assignWith=Aa,Mn.at=ka,Mn.before=Cs,Mn.bind=Rs,Mn.bindAll=tu,Mn.bindKey=zs,Mn.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Hs(e)?e:[e]},Mn.chain=ds,Mn.chunk=function(e,t,n){t=(n?wi(e,t,n):t===i)?1:gn(ja(t),0);var o=null==e?0:e.length;if(!o||t<1)return[];for(var s=0,a=0,u=r(jt(o/t));s<o;)u[a++]=oo(e,s,s+=t);return u},Mn.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,o=[];++t<n;){var i=e[t];i&&(o[r++]=i)}return o},Mn.concat=function(){var e=arguments.length;if(!e)return[];for(var t=r(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return Lt(Hs(n)?Ro(n):[n],yr(t,1))},Mn.cond=function(e){var t=null==e?0:e.length,n=ci();return e=t?Ft(e,(function(e){if("function"!=typeof e[1])throw new Re(s);return[n(e[0]),e[1]]})):[],Kr((function(n){for(var r=-1;++r<t;){var o=e[r];if(St(o[0],this,n))return St(o[1],this,n)}}))},Mn.conforms=function(e){return function(e){var t=Ea(e);return function(n){return cr(n,e,t)}}(lr(e,1))},Mn.constant=nu,Mn.countBy=ms,Mn.create=function(e,t){var n=Nn(e);return null==t?n:ir(n,t)},Mn.curry=function e(t,n,r){var o=$o(t,8,i,i,i,i,i,n=r?i:n);return o.placeholder=e.placeholder,o},Mn.curryRight=function e(t,n,r){var o=$o(t,l,i,i,i,i,i,n=r?i:n);return o.placeholder=e.placeholder,o},Mn.debounce=Es,Mn.defaults=Sa,Mn.defaultsDeep=Ba,Mn.defer=Fs,Mn.delay=Ls,Mn.difference=Ni,Mn.differenceBy=Vi,Mn.differenceWith=Wi,Mn.drop=function(e,t,n){var r=null==e?0:e.length;return r?oo(e,(t=n||t===i?1:ja(t))<0?0:t,r):[]},Mn.dropRight=function(e,t,n){var r=null==e?0:e.length;return r?oo(e,0,(t=r-(t=n||t===i?1:ja(t)))<0?0:t):[]},Mn.dropRightWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!0,!0):[]},Mn.dropWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!0):[]},Mn.fill=function(e,t,n,r){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&wi(e,t,n)&&(n=0,r=o),function(e,t,n,r){var o=e.length;for((n=ja(n))<0&&(n=-n>o?0:o+n),(r=r===i||r>o?o:ja(r))<0&&(r+=o),r=n>r?0:ma(r);n<r;)e[n++]=t;return e}(e,t,n,r)):[]},Mn.filter=function(e,t){return(Hs(e)?Rt:mr)(e,ci(t,3))},Mn.flatMap=function(e,t){return yr(ks(e,t),1)},Mn.flatMapDeep=function(e,t){return yr(ks(e,t),d)},Mn.flatMapDepth=function(e,t,n){return n=n===i?1:ja(n),yr(ks(e,t),n)},Mn.flatten=Yi,Mn.flattenDeep=function(e){return(null==e?0:e.length)?yr(e,d):[]},Mn.flattenDepth=function(e,t){return(null==e?0:e.length)?yr(e,t=t===i?1:ja(t)):[]},Mn.flip=function(e){return $o(e,512)},Mn.flow=ru,Mn.flowRight=ou,Mn.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r},Mn.functions=function(e){return null==e?[]:xr(e,Ea(e))},Mn.functionsIn=function(e){return null==e?[]:xr(e,Fa(e))},Mn.groupBy=_s,Mn.initial=function(e){return(null==e?0:e.length)?oo(e,0,-1):[]},Mn.intersection=Gi,Mn.intersectionBy=Zi,Mn.intersectionWith=Ki,Mn.invert=Ca,Mn.invertBy=Ra,Mn.invokeMap=xs,Mn.iteratee=su,Mn.keyBy=As,Mn.keys=Ea,Mn.keysIn=Fa,Mn.map=ks,Mn.mapKeys=function(e,t){var n={};return t=ci(t,3),wr(e,(function(e,r,o){sr(n,t(e,r,o),e)})),n},Mn.mapValues=function(e,t){var n={};return t=ci(t,3),wr(e,(function(e,r,o){sr(n,r,t(e,r,o))})),n},Mn.matches=function(e){return Mr(lr(e,1))},Mn.matchesProperty=function(e,t){return Nr(e,lr(t,1))},Mn.memoize=Is,Mn.merge=La,Mn.mergeWith=Ia,Mn.method=au,Mn.methodOf=uu,Mn.mixin=lu,Mn.negate=Js,Mn.nthArg=function(e){return e=ja(e),Kr((function(t){return Wr(t,e)}))},Mn.omit=Ja,Mn.omitBy=function(e,t){return qa(e,Js(ci(t)))},Mn.once=function(e){return Cs(2,e)},Mn.orderBy=function(e,t,n,r){return null==e?[]:(Hs(t)||(t=null==t?[]:[t]),Hs(n=r?i:n)||(n=null==n?[]:[n]),Pr(e,t,n))},Mn.over=fu,Mn.overArgs=Us,Mn.overEvery=vu,Mn.overSome=pu,Mn.partial=qs,Mn.partialRight=Ds,Mn.partition=Ss,Mn.pick=Ua,Mn.pickBy=qa,Mn.property=du,Mn.propertyOf=function(e){return function(t){return null==e?i:Ar(e,t)}},Mn.pull=$i,Mn.pullAll=es,Mn.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Yr(e,t,ci(n,2)):e},Mn.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Yr(e,t,i,n):e},Mn.pullAt=ts,Mn.range=hu,Mn.rangeRight=ju,Mn.rearg=Ms,Mn.reject=function(e,t){return(Hs(e)?Rt:mr)(e,Js(ci(t,3)))},Mn.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,o=[],i=e.length;for(t=ci(t,3);++r<i;){var s=e[r];t(s,r,e)&&(n.push(s),o.push(r))}return Qr(e,o),n},Mn.rest=function(e,t){if("function"!=typeof e)throw new Re(s);return Kr(e,t=t===i?t:ja(t))},Mn.reverse=ns,Mn.sampleSize=function(e,t,n){return t=(n?wi(e,t,n):t===i)?1:ja(t),(Hs(e)?$n:$r)(e,t)},Mn.set=function(e,t,n){return null==e?e:eo(e,t,n)},Mn.setWith=function(e,t,n,r){return r="function"==typeof r?r:i,null==e?e:eo(e,t,n,r)},Mn.shuffle=function(e){return(Hs(e)?er:ro)(e)},Mn.slice=function(e,t,n){var r=null==e?0:e.length;return r?(n&&"number"!=typeof n&&wi(e,t,n)?(t=0,n=r):(t=null==t?0:ja(t),n=n===i?r:ja(n)),oo(e,t,n)):[]},Mn.sortBy=Bs,Mn.sortedUniq=function(e){return e&&e.length?uo(e):[]},Mn.sortedUniqBy=function(e,t){return e&&e.length?uo(e,ci(t,2)):[]},Mn.split=function(e,t,n){return n&&"number"!=typeof n&&wi(e,t,n)&&(t=n=i),(n=n===i?m:n>>>0)?(e=ba(e))&&("string"==typeof t||null!=t&&!aa(t))&&!(t=co(t))&&an(e)?xo(dn(e),0,n):e.split(t,n):[]},Mn.spread=function(e,t){if("function"!=typeof e)throw new Re(s);return t=null==t?0:gn(ja(t),0),Kr((function(n){var r=n[t],o=xo(n,0,t);return r&&Lt(o,r),St(e,this,o)}))},Mn.tail=function(e){var t=null==e?0:e.length;return t?oo(e,1,t):[]},Mn.take=function(e,t,n){return e&&e.length?oo(e,0,(t=n||t===i?1:ja(t))<0?0:t):[]},Mn.takeRight=function(e,t,n){var r=null==e?0:e.length;return r?oo(e,(t=r-(t=n||t===i?1:ja(t)))<0?0:t,r):[]},Mn.takeRightWhile=function(e,t){return e&&e.length?ho(e,ci(t,3),!1,!0):[]},Mn.takeWhile=function(e,t){return e&&e.length?ho(e,ci(t,3)):[]},Mn.tap=function(e,t){return t(e),e},Mn.throttle=function(e,t,n){var r=!0,o=!0;if("function"!=typeof e)throw new Re(s);return na(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),Es(e,t,{leading:r,maxWait:t,trailing:o})},Mn.thru=hs,Mn.toArray=da,Mn.toPairs=Da,Mn.toPairsIn=Ma,Mn.toPath=function(e){return Hs(e)?Ft(e,qi):ca(e)?[e]:Ro(Ui(ba(e)))},Mn.toPlainObject=ga,Mn.transform=function(e,t,n){var r=Hs(e),o=r||Zs(e)||fa(e);if(t=ci(t,4),null==n){var i=e&&e.constructor;n=o?r?new i:[]:na(e)&&$s(i)?Nn(Qe(e)):{}}return(o?Tt:wr)(e,(function(e,r,o){return t(n,e,r,o)})),n},Mn.unary=function(e){return Os(e,1)},Mn.union=rs,Mn.unionBy=os,Mn.unionWith=is,Mn.uniq=function(e){return e&&e.length?fo(e):[]},Mn.uniqBy=function(e,t){return e&&e.length?fo(e,ci(t,2)):[]},Mn.uniqWith=function(e,t){return t="function"==typeof t?t:i,e&&e.length?fo(e,i,t):[]},Mn.unset=function(e,t){return null==e||vo(e,t)},Mn.unzip=ss,Mn.unzipWith=as,Mn.update=function(e,t,n){return null==e?e:po(e,t,bo(n))},Mn.updateWith=function(e,t,n,r){return r="function"==typeof r?r:i,null==e?e:po(e,t,bo(n),r)},Mn.values=Na,Mn.valuesIn=function(e){return null==e?[]:Xt(e,Fa(e))},Mn.without=us,Mn.words=$a,Mn.wrap=function(e,t){return qs(bo(t),e)},Mn.xor=ls,Mn.xorBy=cs,Mn.xorWith=fs,Mn.zip=vs,Mn.zipObject=function(e,t){return yo(e||[],t||[],nr)},Mn.zipObjectDeep=function(e,t){return yo(e||[],t||[],eo)},Mn.zipWith=ps,Mn.entries=Da,Mn.entriesIn=Ma,Mn.extend=_a,Mn.extendWith=xa,lu(Mn,Mn),Mn.add=gu,Mn.attempt=eu,Mn.camelCase=Va,Mn.capitalize=Wa,Mn.ceil=bu,Mn.clamp=function(e,t,n){return n===i&&(n=t,t=i),n!==i&&(n=(n=ya(n))==n?n:0),t!==i&&(t=(t=ya(t))==t?t:0),ur(ya(e),t,n)},Mn.clone=function(e){return lr(e,4)},Mn.cloneDeep=function(e){return lr(e,5)},Mn.cloneDeepWith=function(e,t){return lr(e,5,t="function"==typeof t?t:i)},Mn.cloneWith=function(e,t){return lr(e,4,t="function"==typeof t?t:i)},Mn.conformsTo=function(e,t){return null==t||cr(e,t,Ea(t))},Mn.deburr=Pa,Mn.defaultTo=function(e,t){return null==e||e!=e?t:e},Mn.divide=wu,Mn.endsWith=function(e,t,n){e=ba(e),t=co(t);var r=e.length,o=n=n===i?r:ur(ja(n),0,r);return(n-=t.length)>=0&&e.slice(n,o)==t},Mn.eq=Ns,Mn.escape=function(e){return(e=ba(e))&&X.test(e)?e.replace(Z,on):e},Mn.escapeRegExp=function(e){return(e=ba(e))&&se.test(e)?e.replace(ie,"\\$&"):e},Mn.every=function(e,t,n){var r=Hs(e)?Ct:hr;return n&&wi(e,t,n)&&(t=i),r(e,ci(t,3))},Mn.find=ys,Mn.findIndex=Pi,Mn.findKey=function(e,t){return Dt(e,ci(t,3),wr)},Mn.findLast=gs,Mn.findLastIndex=Hi,Mn.findLastKey=function(e,t){return Dt(e,ci(t,3),_r)},Mn.floor=_u,Mn.forEach=bs,Mn.forEachRight=ws,Mn.forIn=function(e,t){return null==e?e:gr(e,ci(t,3),Fa)},Mn.forInRight=function(e,t){return null==e?e:br(e,ci(t,3),Fa)},Mn.forOwn=function(e,t){return e&&wr(e,ci(t,3))},Mn.forOwnRight=function(e,t){return e&&_r(e,ci(t,3))},Mn.get=Ta,Mn.gt=Vs,Mn.gte=Ws,Mn.has=function(e,t){return null!=e&&mi(e,t,Tr)},Mn.hasIn=Oa,Mn.head=Qi,Mn.identity=iu,Mn.includes=function(e,t,n,r){e=Qs(e)?e:Na(e),n=n&&!r?ja(n):0;var o=e.length;return n<0&&(n=gn(o+n,0)),la(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&Nt(e,t,n)>-1},Mn.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:ja(n);return o<0&&(o=gn(r+o,0)),Nt(e,t,o)},Mn.inRange=function(e,t,n){return t=ha(t),n===i?(n=t,t=0):n=ha(n),function(e,t,n){return e>=bn(t,n)&&e<gn(t,n)}(e=ya(e),t,n)},Mn.invoke=za,Mn.isArguments=Ps,Mn.isArray=Hs,Mn.isArrayBuffer=Ys,Mn.isArrayLike=Qs,Mn.isArrayLikeObject=Gs,Mn.isBoolean=function(e){return!0===e||!1===e||ra(e)&&Sr(e)==w},Mn.isBuffer=Zs,Mn.isDate=Ks,Mn.isElement=function(e){return ra(e)&&1===e.nodeType&&!sa(e)},Mn.isEmpty=function(e){if(null==e)return!0;if(Qs(e)&&(Hs(e)||"string"==typeof e||"function"==typeof e.splice||Zs(e)||fa(e)||Ps(e)))return!e.length;var t=ji(e);if(t==S||t==R)return!e.size;if(ki(e))return!Jr(e).length;for(var n in e)if(Je.call(e,n))return!1;return!0},Mn.isEqual=function(e,t){return Er(e,t)},Mn.isEqualWith=function(e,t,n){var r=(n="function"==typeof n?n:i)?n(e,t):i;return r===i?Er(e,t,i,n):!!r},Mn.isError=Xs,Mn.isFinite=function(e){return"number"==typeof e&&Yt(e)},Mn.isFunction=$s,Mn.isInteger=ea,Mn.isLength=ta,Mn.isMap=oa,Mn.isMatch=function(e,t){return e===t||Fr(e,t,vi(t))},Mn.isMatchWith=function(e,t,n){return n="function"==typeof n?n:i,Fr(e,t,vi(t),n)},Mn.isNaN=function(e){return ia(e)&&e!=+e},Mn.isNative=function(e){if(Ai(e))throw new ke("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Lr(e)},Mn.isNil=function(e){return null==e},Mn.isNull=function(e){return null===e},Mn.isNumber=ia,Mn.isObject=na,Mn.isObjectLike=ra,Mn.isPlainObject=sa,Mn.isRegExp=aa,Mn.isSafeInteger=function(e){return ea(e)&&e>=-9007199254740991&&e<=h},Mn.isSet=ua,Mn.isString=la,Mn.isSymbol=ca,Mn.isTypedArray=fa,Mn.isUndefined=function(e){return e===i},Mn.isWeakMap=function(e){return ra(e)&&ji(e)==F},Mn.isWeakSet=function(e){return ra(e)&&"[object WeakSet]"==Sr(e)},Mn.join=function(e,t){return null==e?"":mn.call(e,t)},Mn.kebabCase=Ha,Mn.last=Xi,Mn.lastIndexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=r;return n!==i&&(o=(o=ja(n))<0?gn(r+o,0):bn(o,r-1)),t==t?function(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}(e,t,o):Mt(e,Wt,o,!0)},Mn.lowerCase=Ya,Mn.lowerFirst=Qa,Mn.lt=va,Mn.lte=pa,Mn.max=function(e){return e&&e.length?jr(e,iu,Br):i},Mn.maxBy=function(e,t){return e&&e.length?jr(e,ci(t,2),Br):i},Mn.mean=function(e){return Pt(e,iu)},Mn.meanBy=function(e,t){return Pt(e,ci(t,2))},Mn.min=function(e){return e&&e.length?jr(e,iu,qr):i},Mn.minBy=function(e,t){return e&&e.length?jr(e,ci(t,2),qr):i},Mn.stubArray=mu,Mn.stubFalse=yu,Mn.stubObject=function(){return{}},Mn.stubString=function(){return""},Mn.stubTrue=function(){return!0},Mn.multiply=Au,Mn.nth=function(e,t){return e&&e.length?Wr(e,ja(t)):i},Mn.noConflict=function(){return dt._===this&&(dt._=Ne),this},Mn.noop=cu,Mn.now=Ts,Mn.pad=function(e,t,n){e=ba(e);var r=(t=ja(t))?pn(e):0;if(!t||r>=t)return e;var o=(t-r)/2;return Ho(yt(o),n)+e+Ho(jt(o),n)},Mn.padEnd=function(e,t,n){e=ba(e);var r=(t=ja(t))?pn(e):0;return t&&r<t?e+Ho(t-r,n):e},Mn.padStart=function(e,t,n){e=ba(e);var r=(t=ja(t))?pn(e):0;return t&&r<t?Ho(t-r,n)+e:e},Mn.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),_n(ba(e).replace(ue,""),t||0)},Mn.random=function(e,t,n){if(n&&"boolean"!=typeof n&&wi(e,t,n)&&(t=n=i),n===i&&("boolean"==typeof t?(n=t,t=i):"boolean"==typeof e&&(n=e,e=i)),e===i&&t===i?(e=0,t=1):(e=ha(e),t===i?(t=e,e=0):t=ha(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var o=xn();return bn(e+o*(t-e+ct("1e-"+((o+"").length-1))),t)}return Gr(e,t)},Mn.reduce=function(e,t,n){var r=Hs(e)?It:Qt,o=arguments.length<3;return r(e,ci(t,4),n,o,pr)},Mn.reduceRight=function(e,t,n){var r=Hs(e)?Jt:Qt,o=arguments.length<3;return r(e,ci(t,4),n,o,dr)},Mn.repeat=function(e,t,n){return t=(n?wi(e,t,n):t===i)?1:ja(t),Zr(ba(e),t)},Mn.replace=function(){var e=arguments,t=ba(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Mn.result=function(e,t,n){var r=-1,o=(t=wo(t,e)).length;for(o||(o=1,e=i);++r<o;){var s=null==e?i:e[qi(t[r])];s===i&&(r=o,s=n),e=$s(s)?s.call(e):s}return e},Mn.round=ku,Mn.runInContext=e,Mn.sample=function(e){return(Hs(e)?Xn:Xr)(e)},Mn.size=function(e){if(null==e)return 0;if(Qs(e))return la(e)?pn(e):e.length;var t=ji(e);return t==S||t==R?e.size:Jr(e).length},Mn.snakeCase=Ga,Mn.some=function(e,t,n){var r=Hs(e)?Ut:io;return n&&wi(e,t,n)&&(t=i),r(e,ci(t,3))},Mn.sortedIndex=function(e,t){return so(e,t)},Mn.sortedIndexBy=function(e,t,n){return ao(e,t,ci(n,2))},Mn.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=so(e,t);if(r<n&&Ns(e[r],t))return r}return-1},Mn.sortedLastIndex=function(e,t){return so(e,t,!0)},Mn.sortedLastIndexBy=function(e,t,n){return ao(e,t,ci(n,2),!0)},Mn.sortedLastIndexOf=function(e,t){if(null==e?0:e.length){var n=so(e,t,!0)-1;if(Ns(e[n],t))return n}return-1},Mn.startCase=Za,Mn.startsWith=function(e,t,n){return e=ba(e),n=null==n?0:ur(ja(n),0,e.length),t=co(t),e.slice(n,n+t.length)==t},Mn.subtract=Su,Mn.sum=function(e){return e&&e.length?Gt(e,iu):0},Mn.sumBy=function(e,t){return e&&e.length?Gt(e,ci(t,2)):0},Mn.template=function(e,t,n){var r=Mn.templateSettings;n&&wi(e,t,n)&&(t=i),e=ba(e),t=xa({},t,r,ei);var o,s,a=xa({},t.imports,r.imports,ei),u=Ea(a),l=Xt(a,u),c=0,f=t.interpolate||xe,v="__p += '",p=Oe((t.escape||xe).source+"|"+f.source+"|"+(f===te?he:xe).source+"|"+(t.evaluate||xe).source+"|$","g"),d="//# sourceURL="+("sourceURL"in t?t.sourceURL:"lodash.templateSources["+ ++st+"]")+"\n";e.replace(p,(function(t,n,r,i,a,u){return r||(r=i),v+=e.slice(c,u).replace(Ae,sn),n&&(o=!0,v+="' +\n__e("+n+") +\n'"),a&&(s=!0,v+="';\n"+a+";\n__p += '"),r&&(v+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),c=u+t.length,t})),v+="';\n";var h=t.variable;h||(v="with (obj) {\n"+v+"\n}\n"),v=(s?v.replace(H,""):v).replace(Y,"$1").replace(Q,"$1;"),v="function("+(h||"obj")+") {\n"+(h?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(o?", __e = _.escape":"")+(s?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+v+"return __p\n}";var j=eu((function(){return Se(u,d+"return "+v).apply(i,l)}));if(j.source=v,Xs(j))throw j;return j},Mn.times=function(e,t){if((e=ja(e))<1||e>h)return[];var n=m,r=bn(e,m);t=ci(t),e-=m;for(var o=Zt(r,t);++n<e;)t(n);return o},Mn.toFinite=ha,Mn.toInteger=ja,Mn.toLength=ma,Mn.toLower=function(e){return ba(e).toLowerCase()},Mn.toNumber=ya,Mn.toSafeInteger=function(e){return e?ur(ja(e),-9007199254740991,h):0===e?e:0},Mn.toString=ba,Mn.toUpper=function(e){return ba(e).toUpperCase()},Mn.trim=function(e,t,n){if((e=ba(e))&&(n||t===i))return e.replace(ae,"");if(!e||!(t=co(t)))return e;var r=dn(e),o=dn(t);return xo(r,en(r,o),tn(r,o)+1).join("")},Mn.trimEnd=function(e,t,n){if((e=ba(e))&&(n||t===i))return e.replace(le,"");if(!e||!(t=co(t)))return e;var r=dn(e);return xo(r,0,tn(r,dn(t))+1).join("")},Mn.trimStart=function(e,t,n){if((e=ba(e))&&(n||t===i))return e.replace(ue,"");if(!e||!(t=co(t)))return e;var r=dn(e);return xo(r,en(r,dn(t))).join("")},Mn.truncate=function(e,t){var n=30,r="...";if(na(t)){var o="separator"in t?t.separator:o;n="length"in t?ja(t.length):n,r="omission"in t?co(t.omission):r}var s=(e=ba(e)).length;if(an(e)){var a=dn(e);s=a.length}if(n>=s)return e;var u=n-pn(r);if(u<1)return r;var l=a?xo(a,0,u).join(""):e.slice(0,u);if(o===i)return l+r;if(a&&(u+=l.length-u),aa(o)){if(e.slice(u).search(o)){var c,f=l;for(o.global||(o=Oe(o.source,ba(je.exec(o))+"g")),o.lastIndex=0;c=o.exec(f);)var v=c.index;l=l.slice(0,v===i?u:v)}}else if(e.indexOf(co(o),u)!=u){var p=l.lastIndexOf(o);p>-1&&(l=l.slice(0,p))}return l+r},Mn.unescape=function(e){return(e=ba(e))&&K.test(e)?e.replace(G,hn):e},Mn.uniqueId=function(e){var t=++Ue;return ba(e)+t},Mn.upperCase=Ka,Mn.upperFirst=Xa,Mn.each=bs,Mn.eachRight=ws,Mn.first=Qi,lu(Mn,(xu={},wr(Mn,(function(e,t){Je.call(Mn.prototype,t)||(xu[t]=e)})),xu),{chain:!1}),Mn.VERSION="4.17.11",Tt(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Mn[e].placeholder=Mn})),Tt(["drop","take"],(function(e,t){Pn.prototype[e]=function(n){n=n===i?1:gn(ja(n),0);var r=this.__filtered__&&!t?new Pn(this):this.clone();return r.__filtered__?r.__takeCount__=bn(n,r.__takeCount__):r.__views__.push({size:bn(n,m),type:e+(r.__dir__<0?"Right":"")}),r},Pn.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),Tt(["filter","map","takeWhile"],(function(e,t){var n=t+1,r=1==n||3==n;Pn.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:ci(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}})),Tt(["head","last"],(function(e,t){var n="take"+(t?"Right":"");Pn.prototype[e]=function(){return this[n](1).value()[0]}})),Tt(["initial","tail"],(function(e,t){var n="drop"+(t?"":"Right");Pn.prototype[e]=function(){return this.__filtered__?new Pn(this):this[n](1)}})),Pn.prototype.compact=function(){return this.filter(iu)},Pn.prototype.find=function(e){return this.filter(e).head()},Pn.prototype.findLast=function(e){return this.reverse().find(e)},Pn.prototype.invokeMap=Kr((function(e,t){return"function"==typeof e?new Pn(this):this.map((function(n){return Rr(n,e,t)}))})),Pn.prototype.reject=function(e){return this.filter(Js(ci(e)))},Pn.prototype.slice=function(e,t){e=ja(e);var n=this;return n.__filtered__&&(e>0||t<0)?new Pn(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==i&&(n=(t=ja(t))<0?n.dropRight(-t):n.take(t-e)),n)},Pn.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Pn.prototype.toArray=function(){return this.take(m)},wr(Pn.prototype,(function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),o=Mn[r?"take"+("last"==t?"Right":""):t],s=r||/^find/.test(t);o&&(Mn.prototype[t]=function(){var t=this.__wrapped__,a=r?[1]:arguments,u=t instanceof Pn,l=a[0],c=u||Hs(t),f=function(e){var t=o.apply(Mn,Lt([e],a));return r&&v?t[0]:t};c&&n&&"function"==typeof l&&1!=l.length&&(u=c=!1);var v=this.__chain__,p=!!this.__actions__.length,d=s&&!v,h=u&&!p;if(!s&&c){t=h?t:new Pn(this);var j=e.apply(t,a);return j.__actions__.push({func:hs,args:[f],thisArg:i}),new Wn(j,v)}return d&&h?e.apply(this,a):(j=this.thru(f),d?r?j.value()[0]:j.value():j)})})),Tt(["pop","push","shift","sort","splice","unshift"],(function(e){var t=ze[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);Mn.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var o=this.value();return t.apply(Hs(o)?o:[],e)}return this[n]((function(n){return t.apply(Hs(n)?n:[],e)}))}})),wr(Pn.prototype,(function(e,t){var n=Mn[t];if(n){var r=n.name+"";(zn[r]||(zn[r]=[])).push({name:t,func:n})}})),zn[No(i,2).name]=[{name:"wrapper",func:i}],Pn.prototype.clone=function(){var e=new Pn(this.__wrapped__);return e.__actions__=Ro(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ro(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ro(this.__views__),e},Pn.prototype.reverse=function(){if(this.__filtered__){var e=new Pn(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Pn.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Hs(e),r=t<0,o=n?e.length:0,i=function(e,t,n){var r=-1,o=n.length;for(;++r<o;){var i=n[r],s=i.size;switch(i.type){case"drop":e+=s;break;case"dropRight":t-=s;break;case"take":t=bn(t,e+s);break;case"takeRight":e=gn(e,t-s)}}return{start:e,end:t}}(0,o,this.__views__),s=i.start,a=i.end,u=a-s,l=r?a:s-1,c=this.__iteratees__,f=c.length,v=0,p=bn(u,this.__takeCount__);if(!n||!r&&o==u&&p==u)return jo(e,this.__actions__);var d=[];e:for(;u--&&v<p;){for(var h=-1,j=e[l+=t];++h<f;){var m=c[h],y=m.iteratee,g=m.type,b=y(j);if(2==g)j=b;else if(!b){if(1==g)continue e;break e}}d[v++]=j}return d},Mn.prototype.at=js,Mn.prototype.chain=function(){return ds(this)},Mn.prototype.commit=function(){return new Wn(this.value(),this.__chain__)},Mn.prototype.next=function(){this.__values__===i&&(this.__values__=da(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?i:this.__values__[this.__index__++]}},Mn.prototype.plant=function(e){for(var t,n=this;n instanceof Vn;){var r=Mi(n);r.__index__=0,r.__values__=i,t?o.__wrapped__=r:t=r;var o=r;n=n.__wrapped__}return o.__wrapped__=e,t},Mn.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Pn){var t=e;return this.__actions__.length&&(t=new Pn(this)),(t=t.reverse()).__actions__.push({func:hs,args:[ns],thisArg:i}),new Wn(t,this.__chain__)}return this.thru(ns)},Mn.prototype.toJSON=Mn.prototype.valueOf=Mn.prototype.value=function(){return jo(this.__wrapped__,this.__actions__)},Mn.prototype.first=Mn.prototype.head,tt&&(Mn.prototype[tt]=function(){return this}),Mn}();dt._=jn,(o=function(){return jn}.call(t,n,t,r))===i||(r.exports=o)}).call(this)}).call(this,n("yLpj"),n("YuTi")(e))},MLWZ:function(e,t,n){"use strict";var r=n("xTJ+");function o(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var i;if(n)i=n(t);else if(r.isURLSearchParams(t))i=t.toString();else{var s=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)&&(t+="[]"),r.isArray(e)||(e=[e]),r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),s.push(o(t)+"="+o(e))})))})),i=s.join("&")}return i&&(e+=(-1===e.indexOf("?")?"?":"&")+i),e}},OH9c:function(e,t,n){"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e}},OKyS:function(e,t,n){var r=n("LboF"),o=n("8MIm");"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[e.i,o,""]]);var i={insert:"head",singleton:!1};r(o,i);e.exports=o.locals||{}},OTTw:function(e,t,n){"use strict";var r=n("xTJ+");e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},"Rn+g":function(e,t,n){"use strict";var r=n("LYNF");e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},UBF9:function(e,t,n){"use strict";var r=n("8eWk");n.n(r).a},UnBK:function(e,t,n){"use strict";var r=n("xTJ+"),o=n("xAGQ"),i=n("Lmem"),s=n("JEQr"),a=n("2SVd"),u=n("5oMp");function l(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return l(e),e.baseURL&&!a(e.url)&&(e.url=u(e.baseURL,e.url)),e.headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers||{}),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||s.adapter)(e).then((function(t){return l(e),t.data=o(t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(l(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},YuTi:function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},endd:function(e,t,n){"use strict";function r(e){this.message=e}r.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},r.prototype.__CANCEL__=!0,e.exports=r},eqyj:function(e,t,n){"use strict";var r=n("xTJ+");e.exports=r.isStandardBrowserEnv()?{write:function(e,t,n,o,i,s){var a=[];a.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),r.isString(o)&&a.push("path="+o),r.isString(i)&&a.push("domain="+i),!0===s&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},"jfS+":function(e,t,n){"use strict";var r=n("endd");function o(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e;return{token:new o((function(t){e=t})),cancel:e}},e.exports=o},n6bm:function(e,t,n){"use strict";function r(){this.message="String contains an invalid character"}r.prototype=new Error,r.prototype.code=5,r.prototype.name="InvalidCharacterError",e.exports=function(e){for(var t,n,o=String(e),i="",s=0,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";o.charAt(0|s)||(a="=",s%1);i+=a.charAt(63&t>>8-s%1*8)){if((n=o.charCodeAt(s+=3/4))>255)throw new r;t=t<<8|n}return i}},qFNJ:function(e,t,n){(e.exports=n("I1BE")(void 0)).push([e.i,'.video-wrapper .show-time .video-js .vjs-time-control:not(.vjs-remaining-time){padding-left:.5em;padding-right:0;min-width:0;display:block}.video-wrapper .video-js .vjs-remaining-time{display:none}.video-wrapper .live .vjs-progress-control{visibility:hidden}.video-wrapper .av-loading .vjs-big-play-button,.video-wrapper .live .vjs-remaining-time,.video-wrapper .live .vjs-time-control{display:none}.video-wrapper .hide-big-play-button .vjs-big-play-button,.video-wrapper .hide-controls .vjs-control-bar,.video-wrapper .hide-fluent-button .live-player-fluent-btn,.video-wrapper .hide-fullscreen-button .vjs-fullscreen-control,.video-wrapper .hide-pic-in-pic .video-js .vjs-picture-in-picture-control,.video-wrapper .hide-snapshot-button .vjs-snapshot-control,.video-wrapper .hide-stretch-button .live-player-stretch-btn,.video-wrapper .hide-waiting .vjs-seeking .vjs-loading-spinner,.video-wrapper .hide-waiting .vjs-waiting .vjs-loading-spinner{display:none!important}.video-wrapper .alt{position:absolute;left:0;top:0;right:0;bottom:0;vertical-align:middle;background-repeat:no-repeat;background-position:50% 50%;background-size:contain;background-color:#000;color:#fff;text-align:center}.video-wrapper .alt>table{width:100%;height:100%}.video-wrapper .alt>table tr>td{vertical-align:middle}.video-wrapper .video-js .video-slot{font-size:14px}.video-wrapper .video-js .video-title{font-size:14px;position:absolute;top:5px;right:5px;color:#fff;background-color:hsla(0,0%,50%,.5);border-radius:2px;padding:5px;max-width:120px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.video-wrapper .video-js .vjs-control-bar .vjs-button{cursor:pointer;outline:none}.video-wrapper .video-js .vjs-control-bar .vjs-icon-spinner:before{font-size:1.4em;line-height:1.87}.video-wrapper .vjs-error-display{display:none}.video-wrapper .video-js .vjs-time-control{font-size:1.035em;line-height:2.93em}.video-wrapper .vjs-resolution-button{color:#ccc;font-family:VideoJS}.video-wrapper .vjs-resolution-button .vjs-resolution-button-staticlabel:before{content:"\\F110";font-size:1.8em;line-height:1.7em}.video-wrapper .vjs-resolution-button .vjs-resolution-button-label{width:100%;height:.6em;display:block!important}.video-wrapper .vjs-resolution-button .vjs-resolution-button-label-ie{width:100%;height:.4em;display:block!important}.video-wrapper .vjs-resolution-button ul.vjs-menu-content{width:4em!important}.video-wrapper .vjs-resolution-button .vjs-menu{left:0}.video-wrapper .vjs-resolution-button .vjs-menu li{text-transform:none;font-size:1em;line-height:1.5em;font-family:Arial,Helvetica,sans-serif}.video-wrapper .vjs-audio-button{display:none!important}.video-wrapper .video-js .vjs-big-play-button .vjs-icon-placeholder:before,.video-wrapper .vjs-button>.vjs-icon-placeholder:before{position:relative!important}.video-wrapper .video-js .vjs-play-progress:before{top:-.33em!important}@media screen and (-webkit-min-device-pixel-ratio:0){.video-wrapper .video-js .vjs-control-bar{font-size:14px}.video-wrapper .video-js .vjs-slider-vertical .vjs-volume-level:before{left:-.33em!important}}.video-wrapper.video-wrapper-stretch .alt,.video-wrapper.video-wrapper-stretch .video-js .vjs-poster{background-size:100% 100%}',""])},qtAY:function(e,t,n){"use strict";n.r(t);n("OKyS");var r=n("LvDl"),o=n.n(r),i=n("vDqi"),s=n.n(i),a=n("Iab2"),u=n.n(a);videojs.options.controlBar={volumePanel:{inline:!1}},videojs.options.flash.swf="liveplayer.swf",videojs.options.techOrder=["html5","flash"],videojs.browser.IE_VERSION&&videojs.browser.IE_VERSION<11&&(videojs.options.techOrder=["flash","html5"]),videojs.log.level("off"),flvjs.LoggingControl.enableAll=!1;var l={name:"live-player",data:function(){return{player:null,timer:0,fluentBtnCls:"live-player-fluent-btn",stretchBtnCls:"live-player-stretch-btn",snapFromOutside:!1,snapForPoster:!1,snapForReload:!1,showSlot:!1,fluent_:!0,muted_:!0,stretch_:!1,poster_:"",avLoading:!1,resolutionName:{sd:"标清",hd:"高清",fhd:"超清",yh:"原画"},playbackRate_:1}},props:{videoUrl:{type:String,default:""},videoTitle:{type:String,default:""},poster:{default:""},autoplay:{type:Boolean,default:!0},live:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},currentTime:{type:Number,default:0},timeout:{type:Number,default:20},alt:{type:String,default:"无信号"},muted:{type:Boolean,default:!0},aspect:{type:String,default:"16:9"},videoBufferSize:{type:Number,default:204800},audioBufferSize:{type:Number,default:131072},hlsErrorSeek:{type:Number,default:3},pauseWhenHidden:{type:Boolean,default:!0},fluent:{type:Boolean,default:!0},stretch:{type:Boolean,default:!1},loop:{type:Boolean,default:!1},controls:{type:Boolean,default:!0},hideBigPlayButton:{type:Boolean,default:!1},hideSnapshotButton:{type:Boolean,default:!1},hideFullscreenButton:{type:Boolean,default:!1},hideFluentButton:{type:Boolean,default:!1},hideStretchButton:{type:Boolean,default:!1},hidePicInPic:{type:Boolean,default:!0},hideWaiting:{type:Boolean,default:!0},showTime:{type:Boolean,default:function(){return!videojs.browser.IS_IOS&&!videojs.browser.IS_ANDROID}},showCustomButton:{type:Boolean,default:!0},debug:{type:Boolean,default:!1},hasvideo:{type:Boolean,default:void 0},hasaudio:{type:Boolean,default:void 0},resolution:{type:String,default:""},resolutiondefault:{type:String,default:"hd"},reconnect:{type:Boolean,default:!1},playbackRates:{type:Array,default:function(){return videojs.browser.IS_IOS||videojs.browser.IS_ANDROID?[]:[.5,1,2,3]}},playbackRate:{type:Number,default:1},cors:{type:Boolean,default:!0},withCredentials:{type:Boolean,default:!1}},beforeDestroy:function(){this.destroyVideoJS()},deactivated:function(){this.destroyVideoJS()},watch:{videoUrl:function(e){this.reload()},fluent:function(e){this.fluent_=e,this.reload()},muted:function(e){this.muted_=e,this.reload()},stretch:function(e){this.stretch_=e,this.reload()},poster:function(e){this.poster_=e},loop:function(e){this.reload()},currentTime:function(e){this.player&&this.player.currentTime(e)}},mounted:function(){flvjs.reconnect=this.reconnect,this.muted_=this.muted,this.fluent_=this.fluent,this.stretch_=this.stretch,this.poster_=this.poster,this.playbackRate_=this.playbackRate,this.debug&&(videojs.log.level("info"),flvjs.LoggingControl.enableAll=!0),this.initVideoJS()},computed:{videoType:function(){var e="video/mp4";return this.rtmp?e="rtmp/mp4":(this.http||this.ws)&&this.flv?e="video/x-flv":this.m3u8&&(e="application/x-mpegURL"),e},videoWrapperStyle:function(){if("100%"==this.aspect||"fullscreen"==this.aspect)return{width:"100%",height:"100%"};var e=this.aspect.split(":"),t=parseInt(e[0])||16;return{paddingBottom:(parseInt(e[1])||9)/t*100+"%",position:"relative",margin:"0 auto",overflow:"hidden"}},rtmp:function(){return 0==(this.src||"").indexOf("rtmp")},http:function(){return 0==(this.src||"").indexOf("http")},ws:function(){return 0==(this.src||"").indexOf("ws")},mp4:function(){return/\.mp4.*$/.test(this.src||"")},m3u8:function(){return/\.m3u8.*$/.test(this.src||"")},flv:function(){return/\.flv.*$/.test(this.src||"")},src:function(){return this.videoUrl?0===this.videoUrl.indexOf("//")?location.protocol+this.videoUrl:0===this.videoUrl.indexOf("/")?location.protocol+"//"+location.host+this.videoUrl:this.videoUrl:""},blankHtml:function(){return this.poster_&&this.src?'\n                    <div class="alt" style="background-image:url(\''+this.poster_+"');\">\n                    </div>\n                ":'\n                    <div class="alt">\n                        <table>\n                            <tr>\n                                <td>'+this.alt+"</td>\n                            </tr>\n                        </table>\n                    </div>\n                "},videoHtml:function(){return this.src?this.rtmp||this.http||this.flv||this.mp4?'\n                        <video class="video-js vjs-default-skin vjs-big-play-centered" style="width: 100%; height: 100%; '+(this.stretch_?"object-fit: fill;":"")+'"\n                            webkit-playsinline="" playsinline="" x-webkit-airplay="allow" x5-playsinline=""\n                            preload="none" poster="'+this.poster_+'" '+(this.muted_?"muted":"")+" "+(this.loop?"loop":"")+'>\n                            <source src="'+this.src+'" type="'+this.videoType+'"></source>\n                            <p class="vjs-no-js">\n                                To view this video please enable JavaScript, and consider upgrading to a web browser that\n                                <a href="http://videojs.com/html5-video-support/" target="_blank">\n                                    supports HTML5 video\n                                </a>\n                            </p>\n                        </video>\n                    ':'\n                        <canvas style="width: 100%; height: 100%;"></canvas>\n                    ':this.blankHtml}},methods:{isMobile:function(){return videojs.browser.IS_IOS||videojs.browser.IS_ANDROID},hasUsableSWF:function(){return!!(void 0!==window.ActiveXObject?new ActiveXObject("ShockwaveFlash.ShockwaveFlash"):navigator.plugins["Shockwave Flash"])},reload:function(){this.destroyVideoJS(),this.initVideoJS()},loadResolutionlist:function(e){if(""!=this.resolution){for(var t,n=this.resolution.split(","),r=[],o=0;o<n.length;o++)n[o]==this.resolutiondefault&&o,t="yh"==n[o]?this.videoUrl:this.videoUrl.replace(".m3u8","_"+n[o]+".m3u8"),r.push({src:t,type:this.videoType,label:this.resolutionName[n[o]],res:n[o]});e.updateSrc(r),e.on("resolutionchange",(function(){}))}},getCustomBtn:function(e){var t=this.$el.querySelectorAll(".video-js .vjs-control-bar .vjs-button."+e);return t.length>0?t[0]:null},customFlashButton:function(){var e=this;if(!this.getCustomBtn(this.fluentBtnCls)){var t=this.player.controlBar.addChild("button");t.el_.classList.add(this.fluentBtnCls),t.el_.innerText=this.fluent_?"流畅":"极速",t.el_.title=this.fluent_?"点击切换到极速播放":"点击切换到流畅播放";var n=function(){e.fluent_=!e.fluent_,t.el_.innerText=e.fluent_?"流畅":"极速",t.el_.title=e.fluent_?"点击切换到极速播放":"点击切换到流畅播放",e.player.tech_.setFluent(e.fluent_)};t.on("click",n),t.on("tap",n)}if(!this.getCustomBtn(this.stretchBtnCls)){var r=this.player.controlBar.addChild("button");r.el_.classList.add(this.stretchBtnCls),r.el_.innerText=this.stretch_?"拉伸":"标准",r.el_.title=this.stretch_?"点击切换到标准显示":"点击切换到拉伸显示";n=function(){e.stretch_=!e.stretch_,r.el_.innerText=e.stretch_?"拉伸":"标准",r.el_.title=e.stretch_?"点击切换到标准显示":"点击切换到拉伸显示",e.player.tech_.setStretch(e.stretch_)};r.on("click",n),r.on("tap",n)}},customH5Button:function(){var e=this;if(!this.getCustomBtn(this.stretchBtnCls)&&!videojs.browser.IE_VERSION){var t=this.player.controlBar.addChild("button");t.el_.classList.add(this.stretchBtnCls),t.el_.innerText=this.stretch_?"拉伸":"标准",t.el_.title=this.stretch_?"点击切换到标准显示":"点击切换到拉伸显示";var n=function(){e.stretch_=!e.stretch_,t.el_.innerText=e.stretch_?"拉伸":"标准",t.el_.title=e.stretch_?"点击切换到标准显示":"点击切换到拉伸显示";var n=e.$el.querySelector("video");n?n.style.objectFit=e.stretch_?"fill":"":e.player.tech_.setStretch(e.stretch_)};t.on("click",n),t.on("tap",n)}},destroyVideoJS:function(){this.showSlot=!1;var e=this.$el.querySelector(".video-js"),t=this.$el.querySelector(".video-wrapper");if(e&&t){var n=e.querySelector(".video-slot");n&&(e.removeChild(n),t.appendChild(n));var r=e.querySelector(".video-title");r&&(e.removeChild(r),t.appendChild(r))}this.player&&this.player.dispose&&(this.player.dispose(),this.player=null),this.player&&this.player.destroy&&(this.player.destroy(),this.player=null),this.timer&&(clearInterval(this.timer),this.timer=0),this.$el.querySelector(".video-inner").innerHTML=this.blankHtml,this.$emit("update:loading",!1)},getCurrentTime:function(){return this.player&&"function"==typeof this.player.currentTime?this.player.currentTime():-1},snap:function(){this.player&&this.player.cameraButton&&(this.snapFromOutside=!0,this.player.cameraButton.el().click())},play:function(){this.player&&this.player.play()},pause:function(){this.player&&this.player.pause()},paused:function(){return!!this.player&&this.player.paused()},setMuted:function(e){this.player&&this.player.muted(!!e)},getMuted:function(){return!!this.player&&this.player.muted()},setVolume:function(e){this.player&&this.player.volume(e)},getVolume:function(){return this.player?this.player.volume():0},isFullscreen:function(){return!!this.player&&this.player.isFullscreen()},requestFullscreen:function(){this.player&&this.player.requestFullscreen()},exitFullscreen:function(){this.player&&this.player.exitFullscreen()},toggleFullscreen:function(){this.player&&(this.player.isFullscreen()?this.player.exitFullscreen():this.player.requestFullscreen())},convertDataURIToBinary:function(e){for(var t=";base64,",n=e.indexOf(t)+t.length,r=e.substring(n),o=window.atob(r),i=o.length,s=new Uint8Array(i),a=0;a<i;a++)s[a]=o.charCodeAt(a);return s},convertDataURIToMIME:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t="data:",n=";base64,",r=e.indexOf(t);0==r&&(r+=t.length);var o=e.indexOf(n);return r>=0&&o>r?e.substring(r,o):""},saveFile:function(e){if(e&&e.length>100){this.poster_=e;var t=this.$el.querySelector("video");t&&t.setAttribute("poster",e)}if(this.snapFromOutside)return this.$emit("snapOutside",e),this.snapFromOutside=!1,this.snapForPoster=!1,void(this.snapForReload=!1);if(this.snapForPoster)this.snapForPoster=!1;else{if(this.snapForReload)return this.snapForReload=!1,void this.reload();this.$emit("snapInside",e);var n=this.convertDataURIToBinary(e),r=this.convertDataURIToMIME(e),o=".jpg",i=r.indexOf("/");i>0&&(o="."+r.substring(i+1)),u.a.saveAs(new Blob([n],{type:r}),(this.videoTitle||"snap")+o)}},debounceInitVideoJS:o.a.debounce((function(){this.initVideoJS()}),500),initVideoJSOK:function(){var e=this;this.player&&(this.player.muted(this.muted_),this.player.on("pause",(function(){e.$emit("pause",e.player.currentTime())})),this.player.on("snap",(function(t,n){e.saveFile(n)})),this.player.on("volumechange",(function(t,n){e.muted_=e.player.muted()})));var t=this.$el.querySelector(".video-js"),n=this.$el.querySelector(".video-wrapper");if(t&&n){var r=n.querySelector(".video-slot");r&&(n.removeChild(r),t.appendChild(r));var o=n.querySelector(".video-title");o&&(n.removeChild(o),t.appendChild(o))}this.showSlot=!0},initVideoJS:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(this.$el.querySelector(".video-inner").innerHTML=this.videoHtml,this.src)if(this.rtmp||videojs.browser.IE_VERSION&&this.http&&this.flv){var n=document.title;this.player=videojs(this.$el.querySelector("video"),{notSupportedMessage:"您的浏览器没有安装或开启Flash",tech:["flash"],autoplay:this.autoplay,stretch:this.stretch,fluent:this.fluent,controls:!0}),videojs.browser.IS_SAFARI||this.player.snapshot(),this.player.one("ready",(function(){e.initVideoJSOK(),videojs.browser.IE_VERSION&&e.player.tech_.off("fullscreentoggle"),e.$emit("update:loading",!1),document.title=n,e.player.hotkeys({volumeStep:.1,seekStep:5,enableModifiersForNumbers:!1,enableVolumeScroll:!1}),e.player.on("ended",(function(){e.$emit("ended")})),e.player.on("error",(function(t){e.$emit("error",t)})),e.player.on("timeupdate",(function(){e.$emit("timeupdate",e.player.currentTime())})),e.player.on("playing",(function(){e.$emit("play",e.player.currentTime())})),e.showCustomButton&&e.customFlashButton();var t=e.$el.querySelectorAll(".video-js .vjs-control-bar .vjs-button"),r=!0,o=!1,i=void 0;try{for(var s,a=t[Symbol.iterator]();!(r=(s=a.next()).done);r=!0){s.value.addEventListener("mouseup",(function(){e.player.focus()}))}}catch(e){o=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(o)throw i}}if(!e.hasUsableSWF()){var u=e.$el.querySelector(".vjs-poster");u&&(u.style.display="none")}document.getElementsByClassName("vjs-picture-in-picture-control")[0].style.display="none"})),document.title=n,this.player.on("error",(function(t){(t=e.$el.querySelector(".vjs-error .vjs-error-display .vjs-modal-dialog-content")).innerHTML="<a href='http://www.adobe.com/go/getflashplayer' target='_blank'>"+t.textContent+"</a>"}))}else if((this.http||this.ws)&&this.flv){var r={isLive:this.live,cors:this.cors,withCredentials:this.withCredentials},o={enableWorker:!1,enableStashBuffer:!1};null!=this.hasaudio&&(r.hasAudio=this.hasaudio),null!=this.hasvideo&&(r.hasVideo=this.hasvideo),this.player=videojs(this.$el.querySelector("video"),{techOrder:["flvjs","html5"],controls:!0,flvjs:{mediaDataSource:r,config:o}}),videojs.browser.IS_SAFARI||this.player.snapshot(),this.player.one("ready",(function(){e.initVideoJSOK(),e.$emit("update:loading",!1),e.stretch_&&(e.player.tech_.el_.style.objectFit="fill"),e.player.hotkeys({volumeStep:.1,seekStep:5,enableModifiersForNumbers:!1,enableVolumeScroll:!1});e.player.tech_.flvPlayer.on("error",(function(t){!function t(n){e.debug,e.live&&e.src&&(e.avLoading=!0,e.player.isFullscreen()?(e.player.tech_.flvPlayer.unload(),e.player.tech_.flvPlayer.detachMediaElement(),e.player.tech_.flvPlayer.destroy(),e.player.tech_.flvPlayer=window.flvjs.createPlayer(Object.assign({},r,{type:void 0===r.type?"flv":r.type,url:e.videoUrl}),o),e.player.tech_.flvPlayer.attachMediaElement(e.player.tech_.el_),e.player.tech_.flvPlayer.on("error",(function(e){t(e)})),e.player.tech_.flvPlayer.load()):!e.snapForReload&&e.player.cameraButton&&(e.snapForReload=!0,e.player.cameraButton.el().click()))}()})),e.player.on("canplay",(function(){e.autoplay&&e.player.tech_.flvPlayer.play().catch((function(){}))})),e.player.on("timeupdate",(function(){var t=e.player.currentTime();!e.snapForPoster&&!e.poster_&&t>2&&e.player.cameraButton&&(e.snapForPoster=!0,e.player.cameraButton.el().click()),e.$emit("timeupdate",t)})),e.player.on("error",(function(t){e.$emit("error",t)})),e.player.on("ended",(function(){e.live&&(e.avLoading=!0,!e.snapForReload&&e.player.cameraButton&&(e.snapForReload=!0,e.player.cameraButton.el().click())),e.$emit("ended")})),e.player.on("playing",(function(){e.avLoading=!1,e.$emit("play",e.player.currentTime())})),e.showCustomButton&&e.customH5Button();var t=e.$el.querySelectorAll(".video-js .vjs-control-bar .vjs-button"),n=!0,i=!1,s=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done);n=!0){a.value.addEventListener("mouseup",(function(){e.player.focus()}))}}catch(e){i=!0,s=e}finally{try{!n&&u.return&&u.return()}finally{if(i)throw s}}}))}else if(this.http&&this.m3u8){var i=(new Date).getTime();this.$emit("update:loading",!0),this.avLoading=!0;var a=0,u=function(){var n=document.title;e.player||(""!=e.resolution?(-1==e.resolution.indexOf(e.resolutiondefault)&&(e.resolutiondefault="yh"),e.player=videojs(e.$el.querySelector("video"),{autoplay:e.autoplay,stretch:e.stretch,controls:!0,plugins:{videoJsResolutionSwitcher:{default:e.resolutiondefault,dynamicLabel:!0}},playbackRates:e.live?[]:e.playbackRates}),e.loadResolutionlist(e.player)):e.player=videojs(e.$el.querySelector("video"),{autoplay:e.autoplay,stretch:e.stretch,controls:!0,playbackRates:e.live?[]:e.playbackRates}),e.live||1==e.playbackRate_||e.player.playbackRate(e.playbackRate_),e.player.on("ratechange",(function(){e.playbackRate_=e.player.playbackRate()})),videojs.browser.IS_SAFARI||e.player.snapshot(),e.player.one("ready",(function(){e.initVideoJSOK(),a=setTimeout((function(){e.avLoading=!1,e.$emit("update:loading",!1)}),1e3),document.title=n,e.player.hotkeys({volumeStep:.1,seekStep:5,enableModifiersForNumbers:!1,enableVolumeScroll:!1}),e.player.on("ended",(function(){e.$emit("ended")})),e.player.on("timeupdate",(function(){e.$emit("timeupdate",e.player.currentTime())})),e.player.on("playing",(function(){e.$emit("play",e.player.currentTime())})),e.player.one("error",(function(n,r){a&&(clearTimeout(a),a=0),e.$emit("error",n),e.live?setTimeout((function(){e.destroyVideoJS(),e.initVideoJS()}),1e3):e.hlsErrorSeek>0&&(e.destroyVideoJS(),t+=e.hlsErrorSeek,e.initVideoJS(t))})),document.title=n,e.live?e.player&&e.player.reloadSourceOnError():e.player&&(e.currentTime>0||t>0)&&(videojs.browser.IE_VERSION?e.player.on("loadeddata",(function(){setTimeout((function(){e.player.currentTime(e.currentTime+t)}),500)})):e.isMobile()?setTimeout((function(){var n=e.$el.querySelector("video");n&&(n.currentTime=e.currentTime+t)}),3200):e.player.currentTime(e.currentTime+t)),e.autoplay&&setTimeout((function(){var t=e.$el.querySelector(".vjs-big-play-button");if(t){var n=window.getComputedStyle(t);n&&"block"===n.display&&t.click()}}),2e3),e.showCustomButton&&e.customH5Button();var r=e.$el.querySelectorAll(".video-js .vjs-control-bar .vjs-button"),o=!0,i=!1,s=void 0;try{for(var u,l=r[Symbol.iterator]();!(o=(u=l.next()).done);o=!0){u.value.addEventListener("mouseup",(function(){e.player.focus()}))}}catch(e){i=!0,s=e}finally{try{!o&&l.return&&l.return()}finally{if(i)throw s}}})))};this.timer=setInterval((function(){var t=(new Date).getTime();return e.timeout>0&&t-i>1e3*e.timeout?(clearInterval(e.timer),e.timer=0,e.$emit("update:loading",!1),e.avLoading=!1,void e.$emit("message",{type:"error",message:"加载播放资源失败"})):e.src?void s.a.head(e.src).then((function(t){clearInterval(e.timer),e.timer=0,u()})).catch((function(t){e.debug})):(clearInterval(e.timer),e.timer=0,e.$emit("update:loading",!1),void(e.avLoading=!1))}),1e3)}else this.player=videojs(this.$el.querySelector("video"),{autoplay:this.autoplay,stretch:this.stretch,controls:!0,playbackRates:this.live?[]:this.playbackRates}),this.live||1==this.playbackRate_||this.player.playbackRate(this.playbackRate_),this.player.on("ratechange",(function(){e.playbackRate_=e.player.playbackRate()})),videojs.browser.IS_SAFARI||this.player.snapshot(),this.player.one("ready",(function(){e.initVideoJSOK()})),this.player.on("ended",(function(){e.$emit("ended")})),this.player.on("timeupdate",(function(){e.$emit("timeupdate",e.player.currentTime())})),this.player.on("playing",(function(){e.$emit("play",e.player.currentTime())})),this.player.on("error",(function(t){e.$emit("error",t)}))}}};n("UBF9");var c=function(e,t,n,r,o,i,s,a){var u,l="function"==typeof e?e.options:e;if(t&&(l.render=t,l.staticRenderFns=n,l._compiled=!0),r&&(l.functional=!0),i&&(l._scopeId="data-v-"+i),s?(u=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},l._ssrRegister=u):o&&(u=a?function(){o.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:o),u)if(l.functional){l._injectStyles=u;var c=l.render;l.render=function(e,t){return u.call(t),c(e,t)}}else{var f=l.beforeCreate;l.beforeCreate=f?[].concat(f,u):[u]}return{exports:e,options:l}}(l,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"player-wrapper"},[n("div",{class:["video-wrapper",{"video-wrapper-stretch":this.stretch_}],style:e.videoWrapperStyle},[n("div",{staticClass:"video-inner",class:{live:e.live,"av-loading":e.avLoading,"hide-waiting":e.hideWaiting,"hide-controls":!e.controls,"hide-big-play-button":e.hideBigPlayButton,"hide-snapshot-button":e.hideSnapshotButton,"hide-fullscreen-button":e.hideFullscreenButton,"hide-stretch-button":e.hideStretchButton,"hide-fluent-button":e.hideFluentButton,"hide-pic-in-pic":e.hidePicInPic,"show-time":e.showTime&&!e.live},staticStyle:{position:"absolute",top:"0",bottom:"0",left:"0",right:"0"}}),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.showSlot,expression:"showSlot"}],staticClass:"video-slot"},[e._t("default")],2),e._v(" "),n("span",{directives:[{name:"show",rawName:"v-show",value:e.videoTitle&&e.showSlot,expression:"videoTitle && showSlot"}],staticClass:"video-title",attrs:{title:e.videoTitle}},[e._v(e._s(e.videoTitle))])])])}),[],!1,null,null,null).exports;c.install=function(e){"undefined"!=typeof window&&window.Vue&&(e=window.Vue),e.component(c.name,c)};t.default=c},tQ2B:function(e,t,n){"use strict";var r=n("xTJ+"),o=n("Rn+g"),i=n("MLWZ"),s=n("w0Vi"),a=n("OTTw"),u=n("LYNF"),l="undefined"!=typeof window&&window.btoa&&window.btoa.bind(window)||n("n6bm");e.exports=function(e){return new Promise((function(t,c){var f=e.data,v=e.headers;r.isFormData(f)&&delete v["Content-Type"];var p=new XMLHttpRequest,d="onreadystatechange",h=!1;if("undefined"==typeof window||!window.XDomainRequest||"withCredentials"in p||a(e.url)||(p=new window.XDomainRequest,d="onload",h=!0,p.onprogress=function(){},p.ontimeout=function(){}),e.auth){var j=e.auth.username||"",m=e.auth.password||"";v.Authorization="Basic "+l(j+":"+m)}if(p.open(e.method.toUpperCase(),i(e.url,e.params,e.paramsSerializer),!0),p.timeout=e.timeout,p[d]=function(){if(p&&(4===p.readyState||h)&&(0!==p.status||p.responseURL&&0===p.responseURL.indexOf("file:"))){var n="getAllResponseHeaders"in p?s(p.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?p.response:p.responseText,status:1223===p.status?204:p.status,statusText:1223===p.status?"No Content":p.statusText,headers:n,config:e,request:p};o(t,c,r),p=null}},p.onerror=function(){c(u("Network Error",e,null,p)),p=null},p.ontimeout=function(){c(u("timeout of "+e.timeout+"ms exceeded",e,"ECONNABORTED",p)),p=null},r.isStandardBrowserEnv()){var y=n("eqyj"),g=(e.withCredentials||a(e.url))&&e.xsrfCookieName?y.read(e.xsrfCookieName):void 0;g&&(v[e.xsrfHeaderName]=g)}if("setRequestHeader"in p&&r.forEach(v,(function(e,t){void 0===f&&"content-type"===t.toLowerCase()?delete v[t]:p.setRequestHeader(t,e)})),e.withCredentials&&(p.withCredentials=!0),e.responseType)try{p.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&p.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&p.upload&&p.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){p&&(p.abort(),c(e),p=null)})),void 0===f&&(f=null),p.send(f)}))}},vDqi:function(e,t,n){e.exports=n("zuR4")},w0Vi:function(e,t,n){"use strict";var r=n("xTJ+"),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,i,s={};return e?(r.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),n=r.trim(e.substr(i+1)),t){if(s[t]&&o.indexOf(t)>=0)return;s[t]="set-cookie"===t?(s[t]?s[t]:[]).concat([n]):s[t]?s[t]+", "+n:n}})),s):s}},x86X:function(e,t){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},xAGQ:function(e,t,n){"use strict";var r=n("xTJ+");e.exports=function(e,t,n){return r.forEach(n,(function(n){e=n(e,t)})),e}},"xTJ+":function(e,t,n){"use strict";var r=n("HSsa"),o=n("x86X"),i=Object.prototype.toString;function s(e){return"[object Array]"===i.call(e)}function a(e){return null!==e&&"object"==typeof e}function u(e){return"[object Function]"===i.call(e)}function l(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),s(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}e.exports={isArray:s,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:o,isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isUndefined:function(e){return void 0===e},isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:u,isStream:function(e){return a(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:l,merge:function e(){var t={};function n(n,r){"object"==typeof t[r]&&"object"==typeof n?t[r]=e(t[r],n):t[r]=n}for(var r=0,o=arguments.length;r<o;r++)l(arguments[r],n);return t},extend:function(e,t,n){return l(t,(function(t,o){e[o]=n&&"function"==typeof t?r(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")}}},yK9s:function(e,t,n){"use strict";var r=n("xTJ+");e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},yLpj:function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},zuR4:function(e,t,n){"use strict";var r=n("xTJ+"),o=n("HSsa"),i=n("CgaS"),s=n("JEQr");function a(e){var t=new i(e),n=o(i.prototype.request,t);return r.extend(n,i.prototype,t),r.extend(n,t),n}var u=a(s);u.Axios=i,u.create=function(e){return a(r.merge(s,e))},u.Cancel=n("endd"),u.CancelToken=n("jfS+"),u.isCancel=n("Lmem"),u.all=function(e){return Promise.all(e)},u.spread=n("DfZB"),e.exports=u,e.exports.default=u}})}));

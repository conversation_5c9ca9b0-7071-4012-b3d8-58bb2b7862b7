.lxwjzxfw-comp{
    width: 100%;
    height: p(1015);
    background: url('../images/lxwjzxfw-bg.png') center no-repeat;
    background-size: 100% 100%;
    .lxwjzxfw-plan {
         padding: p(0) p(350);
         display: flex;
         flex-direction: column;
    }
    h3 {
         color: #fff;
         font-size: p(22);
         position: relative;
         line-height: p(46);
         div {
              position: absolute;
              bottom: 0;
              left: 0;
              width: p(45);
              height: 2px;
              background-color: #aeaeb7;
         }
    }

    .zxfl-lc{
        margin-top: p(45);
        .box{
            flex: 0.21;
            .icon-plan{
                position: relative;
            }
            .lc-plan{
                width: p(180);
                height: p(82);
                background-image: linear-gradient(140deg, 
                    #e0e4f0 0%, 
                    #b3bad1 100%);
                border-radius: 6px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                p:nth-of-type(1) {
                    font-size: p(20);
                    color: #000000;
                    font-weight: 600;
                }
                p:nth-of-type(2) {
                    font-size: p(16);
                    color: #515c73;
                }
            }
        }
        .box-5{
            flex: 0.16;
        }
        
        .blue-box{
            .lc-plan{
                background-image: linear-gradient(0deg, 
                #3ebbff 0%, 
                #3d7df8 100%);
                p{
                    color: #fff !important;
                }
                p:nth-of-type(1) {
                    font-size: p(20);
                }
                p:nth-of-type(2) {
                    font-size: p(16);
                }
            }
        }
        .lc-top{
            display: flex;
            .box-5{
                .icon-plan{
                    .text-img{
                        position: absolute;
                        width: p(55);
                        height: p(61);
                        top: 50%;
                        transform: translate(0, -50%);
                    }
                }
            }
            .box-3{
                .lc-plan{
                    margin-top: p(39);
                }
                .icon-plan{
                    img{
                        position: absolute;
                        width: p(195);
                        height: p(138);
                        top: p(0);
                        left: p(80);
                    }
                }
            }
            .box-4{
                .lc-plan{
                    width: p(154);
	                height: p(70);
                    margin-top: p(152);
                    margin-left: p(15);
                }
            }

            .icon-plan{
                width: p(180);
                margin: p(10) 0;
                display: flex;
                justify-content: center;
                img{
                    height: p(176);
                    width: p(18);
                }
                &.xxld{
                    width: p(180);
                    background: url('../images//lxwj-xx.png') top no-repeat;
                    background-size: 100% p(127);
                }
            }
        }

        .lc-bottom{
            display: flex;
            .box{
                display: flex;
                align-items: center;
            }
            .icon-plan{
                flex: 1;
                display: flex;
                img{
                    margin: auto;
                    width: p(57);
                    height: p(18);
                }
            }
        }

        .lxwj-text{
            color: #ffffff;
            font-size: p(18);
            margin-top: p(40);
        }
    }

    .xq-box{
        display: flex;
        align-items: flex-end;
        .video-box{
            width: p(517);
	        height: p(290);
            // background-color: #165dff;
            margin-right: p(188);
            iframe{
                width: 100%;
                height: 100%;
            }
        }

        .btn-box{
            margin-top: p(80);
            li{
                width: p(190);
                height: p(55);
                background-color: #165dff;
                border-radius: p(4);
                display: flex;
                align-items: center;
                justify-content: space-evenly;
                margin-bottom: p(40);
                cursor: pointer;
                img{
                    width: p(26);
                }
                p{
                    color: #ffffff;
                    font-size: p(24);
                }

            }
            li:last-child {
                margin-bottom: p(15);
            }
            li:hover{
                background-color: #3f77fc;
            }
        }
    }
}
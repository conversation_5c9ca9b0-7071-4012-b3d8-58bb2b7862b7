<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1.0" />
		<script>
			var now = new Date().getTime()
			var head = document.getElementsByTagName('head')[0]

			var linkList = []
			for (var i = 0; i < linkList.length; i++) {
				var link = document.createElement('link')
				link.href = linkList[i] + '?_t=' + now
				link.rel = 'stylesheet'
				link.type = 'text/css'
				head.appendChild(link)
			}
		</script>
		<script src="./jessibuca.js"></script>
		<script src="./static/liveplayer//liveplayer-lib.min.js"></script>
		<link rel="icon" href="<%= BASE_URL %>logo.png" />
		<title><%= htmlWebpackPlugin.options.title %></title>
	</head>
	<body>
		<noscript>
			<strong
				>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript
				enabled. Please enable it to continue.</strong
			>
		</noscript>
		<div id="app"></div>
		<!-- built files will be auto injected -->
	</body>
</html>

// See www.openfst.org for extensive documentation on this weighted
// finite-state transducer library.
//
// Forward declarations for the FST and FST script classes.

#ifndef FST_SCRIPT_FSTSCRIPT_DECL_H_
#define FST_SCRIPT_FSTSCRIPT_DECL_H_

#include <fst/fst-decl.h>

namespace fst {
namespace script {

class ArcClass;

class ArcIteratorClass;
class MutableArcIteratorClass;

class EncodeMapperClass;

class FstClass;
class MutableFstClass;
class VectorFstClass;

class StateIteratorClass;

class WeightClass;

}  // namespace script
}  // namespace fst;

#endif  // FST_SCRIPT_FSTSCRIPT_DECL_H_

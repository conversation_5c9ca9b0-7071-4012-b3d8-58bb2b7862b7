"""
B站视频下载模块
支持字幕下载和音频下载功能
"""

import os
import re
import yt_dlp
from pathlib import Path
from datetime import datetime
import logging
from utils import get_ffmpeg_path
from urllib.parse import urlparse, parse_qs

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BilibiliDownloader:
    """B站下载器类"""
    
    def __init__(self, mp3_dir="mp3", txt_dir="txt", temp_dir="temp"):
        """
        初始化下载器
        
        Args:
            mp3_dir: 音频文件保存目录
            txt_dir: 文本文件保存目录  
            temp_dir: 临时文件目录
        """
        self.mp3_dir = Path(mp3_dir)
        self.txt_dir = Path(txt_dir)
        self.temp_dir = Path(temp_dir)
        
        # 确保目录存在
        self.mp3_dir.mkdir(exist_ok=True)
        self.txt_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)
    
    def sanitize_filename(self, filename):
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            清理后的文件名
        """
        # 移除或替换非法字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 100:
            filename = filename[:100]
        return filename
    
    def _extract_p_param(self, url):
        """
        从URL中提取p参数
        
        Args:
            url: B站视频URL
            
        Returns:
            p参数值(整数)或None
        """
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        
        # 获取p参数，B站p参数从1开始计数
        if 'p' in query_params and query_params['p'][0].isdigit():
            p_value = int(query_params['p'][0])
            logger.info(f"检测到p参数: {p_value}")
            return p_value
        return None
    
    def get_video_info(self, url):
        """
        获取视频信息

        Args:
            url: B站视频URL

        Returns:
            视频信息字典
        """
        # 提取p参数
        p_param = self._extract_p_param(url)
        
        # 设置yt-dlp选项
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            # B站优化配置
            'socket_timeout': 30,  # 设置超时时间
            'retries': 3,  # 重试次数
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.bilibili.com/',
            },
            'extractor_args': {
                'bilibili': {
                    'api_version': 'web',  # 使用web API版本
                }
            },
        }
        
        # 如果有p参数，设置playlist_items来指定要下载的视频
        if p_param is not None:
            ydl_opts['noplaylist'] = False  # 允许处理播放列表
            ydl_opts['playlist_items'] = str(p_param)  # 指定要下载的视频索引
            logger.info(f"设置playlist_items为: {p_param}")
        else:
            ydl_opts['noplaylist'] = True  # 没有p参数时只下载单个视频
        
        try:
            print("正在连接B站服务器...")
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                print("正在解析视频信息...")
                info = ydl.extract_info(url, download=False)
                
                # 处理合集视频
                if 'entries' in info and isinstance(info['entries'], list):
                    if len(info['entries']) > 0:
                        # 如果指定了p参数，应该已经通过playlist_items筛选了
                        # 但为了安全起见，再次检查
                        if p_param is not None and len(info['entries']) > 1:
                            logger.warning(f"playlist_items设置可能未生效，手动选择第{p_param}个视频")
                            p_index = p_param - 1  # 转换为0基索引
                            if 0 <= p_index < len(info['entries']):
                                info = info['entries'][p_index]
                            else:
                                logger.warning(f"p参数 {p_param} 超出合集范围(1-{len(info['entries'])}), 使用第一个视频")
                                info = info['entries'][0]
                        else:
                            # 如果entries只有一个元素或没有p参数，使用第一个
                            info = info['entries'][0]
                    else:
                        logger.warning("视频合集为空")
                
                print("视频信息解析完成")
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'upload_date': info.get('upload_date', ''),
                    'id': info.get('id', ''),
                    'has_subtitles': bool(info.get('subtitles') or info.get('automatic_captions'))
                }
        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
            print(f"错误详情: {e}")
            return None
    
    def download_subtitles(self, url):
        """
        下载视频字幕

        Args:
            url: B站视频URL

        Returns:
            tuple: (是否成功, 字幕文件路径或错误信息)
        """
        try:
            # 提取p参数
            p_param = self._extract_p_param(url)
            
            # 获取视频信息
            info = self.get_video_info(url)
            if not info:
                return False, "无法获取视频信息"
            
            if not info['has_subtitles']:
                return False, "视频没有可用的字幕"
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = self.sanitize_filename(info['title'])
            subtitle_filename = f"{safe_title}_{timestamp}.txt"
            subtitle_path = self.txt_dir / subtitle_filename
            
            # 下载字幕配置 - B站适配
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitleslangs': ['zh-Hans', 'zh-CN', 'zh', 'en'],  # B站优先中文
                'subtitlesformat': 'best',  # B站可能使用不同格式
                'outtmpl': str(self.temp_dir / '%(title)s.%(ext)s'),
                'quiet': True,
                'socket_timeout': 30,
                'retries': 3,
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.bilibili.com/',
                },
            }
            
            # 如果有p参数，设置playlist_items来指定要下载的视频
            if p_param is not None:
                ydl_opts['noplaylist'] = False  # 允许处理播放列表
                ydl_opts['playlist_items'] = str(p_param)  # 指定要下载的视频索引
            else:
                ydl_opts['noplaylist'] = True  # 没有p参数时只下载单个视频
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])
            
            # 查找下载的字幕文件 - 支持多种格式
            subtitle_files = list(self.temp_dir.glob("*.vtt")) + \
                           list(self.temp_dir.glob("*.srt")) + \
                           list(self.temp_dir.glob("*.ass")) + \
                           list(self.temp_dir.glob("*.json"))

            if not subtitle_files:
                return False, "字幕下载失败"

            # 转换字幕到纯文本
            subtitle_file = subtitle_files[0]
            subtitle_text = self._convert_subtitle_to_text(subtitle_file)

            # 保存纯文本字幕
            with open(subtitle_path, 'w', encoding='utf-8') as f:
                f.write(subtitle_text)

            # 清理临时文件
            subtitle_file.unlink()
            
            logger.info(f"字幕下载成功: {subtitle_path}")
            return True, str(subtitle_path)
            
        except Exception as e:
            logger.error(f"字幕下载失败: {e}")
            return False, str(e)
    
    def _convert_subtitle_to_text(self, subtitle_file):
        """
        将字幕文件转换为纯文本（支持多种格式）

        Args:
            subtitle_file: 字幕文件路径

        Returns:
            纯文本内容
        """
        text_lines = []
        file_ext = subtitle_file.suffix.lower()

        try:
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read()

            if file_ext == '.vtt':
                # VTT格式处理
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    # 跳过时间戳行和空行
                    if (line and
                        not line.startswith('WEBVTT') and
                        not re.match(r'\d{2}:\d{2}:\d{2}\.\d{3}', line) and
                        not line.startswith('NOTE')):
                        text_lines.append(line)

            elif file_ext == '.srt':
                # SRT格式处理
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    # 跳过序号和时间戳行
                    if (line and
                        not line.isdigit() and
                        not re.match(r'\d{2}:\d{2}:\d{2},\d{3}', line)):
                        text_lines.append(line)

            elif file_ext == '.json':
                # JSON格式处理（B站可能使用）
                import json
                try:
                    data = json.loads(content)
                    # 根据B站JSON字幕格式提取文本
                    if isinstance(data, dict) and 'body' in data:
                        for item in data['body']:
                            if 'content' in item:
                                text_lines.append(item['content'])
                    elif isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict) and 'content' in item:
                                text_lines.append(item['content'])
                except json.JSONDecodeError:
                    # 如果JSON解析失败，按普通文本处理
                    text_lines = [content]

            else:
                # 其他格式按普通文本处理
                text_lines = [content]

        except Exception as e:
            logger.error(f"字幕文件转换失败: {e}")
            return "字幕转换失败"

        return '\n'.join(text_lines)
    
    def download_audio(self, url):
        """
        下载视频音频

        Args:
            url: B站视频URL

        Returns:
            tuple: (是否成功, 音频文件路径或错误信息)
        """
        try:
            # 提取p参数
            p_param = self._extract_p_param(url)
            
            # 获取视频信息
            info = self.get_video_info(url)
            if not info:
                return False, "无法获取视频信息"

            # 获取FFmpeg路径
            ffmpeg_path = get_ffmpeg_path()
            if not ffmpeg_path:
                return False, "FFmpeg未找到，请确保ffmpeg/bin/ffmpeg.exe存在"

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = self.sanitize_filename(info['title'])
            audio_filename = f"{safe_title}_{timestamp}.mp3"
            audio_path = self.mp3_dir / audio_filename

            # 下载音频配置
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': str(audio_path.with_suffix('')),
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'quiet': False,
                'ffmpeg_location': str(Path(ffmpeg_path).parent),  # 指定FFmpeg目录
                'socket_timeout': 60,  # 音频下载可能需要更长时间
                'retries': 3,
                'http_headers': {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': 'https://www.bilibili.com/',
                },
            }
            
            # 如果有p参数，设置playlist_items来指定要下载的视频
            if p_param is not None:
                ydl_opts['noplaylist'] = False  # 允许处理播放列表
                ydl_opts['playlist_items'] = str(p_param)  # 指定要下载的视频索引
            else:
                ydl_opts['noplaylist'] = True  # 没有p参数时只下载单个视频

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([url])

            if audio_path.exists():
                logger.info(f"音频下载成功: {audio_path}")
                return True, str(audio_path)
            else:
                return False, "音频文件未生成"

        except Exception as e:
            logger.error(f"音频下载失败: {e}")
            return False, str(e)


def main():
    """测试函数"""
    downloader = BilibiliDownloader()

    # 测试URL
    test_url = input("请输入B站视频URL: ")

    print("正在获取视频信息...")
    info = downloader.get_video_info(test_url)
    if info:
        print(f"视频标题: {info['title']}")
        print(f"时长: {info['duration']}秒")
        print(f"是否有字幕: {info['has_subtitles']}")

    print("\n尝试下载字幕...")
    success, result = downloader.download_subtitles(test_url)
    if success:
        print(f"字幕下载成功: {result}")
    else:
        print(f"字幕下载失败: {result}")
        print("\n开始下载音频...")
        success, result = downloader.download_audio(test_url)
        if success:
            print(f"音频下载成功: {result}")
        else:
            print(f"音频下载失败: {result}")


# 保持向后兼容性的别名
YouTubeDownloader = BilibiliDownloader


if __name__ == "__main__":
    main()

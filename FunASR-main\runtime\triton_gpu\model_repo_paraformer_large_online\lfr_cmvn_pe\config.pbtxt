# Copyright (c) 2021, NVIDIA CORPORATION.  All rights reserved.

# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Created on 2024-01-01
# Author: GuAn Zhu

name: "lfr_cmvn_pe"
backend: "onnxruntime"
default_model_filename: "lfr_cmvn_pe.onnx"

max_batch_size: 128

sequence_batching{
    max_sequence_idle_microseconds: 15000000
    oldest {
      max_candidate_sequences: 1024
      preferred_batch_size: [32, 64, 128]
      max_queue_delay_microseconds: 300
    }
    control_input [
    ]
    state [
    {
      input_name: "cache"
      output_name: "r_cache"
      data_type: TYPE_FP32
      dims: [10, 560]
      initial_state: {
       data_type: TYPE_FP32
       dims: [10, 560]
       zero_data: true
       name: "initial state"
      }
    },
    {
      input_name: "offset"
      output_name: "r_offset"
      data_type: TYPE_INT32
      dims: [1]
      initial_state: {
       data_type: TYPE_INT32
       dims: [1]
       zero_data: true
       name: "initial state"
      }
    }
  ]
}
input [
  {
    name: "chunk_xs"
    data_type: TYPE_FP32
    dims: [61, 80]
  }
]
output [
  {
    name: "chunk_xs_out"
    data_type: TYPE_FP32
    dims: [-1, 560]
  },
  {
    name: "chunk_xs_out_len"
    data_type: TYPE_INT32
    dims: [-1]
  }
]
instance_group [
    {
      count: 1
      kind: KIND_GPU
    }
]


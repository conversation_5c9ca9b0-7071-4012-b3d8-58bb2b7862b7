{"name": "template-vue", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --env VUE_APP_frontendConfigUrl=/template-vue/frontend-config-dev.json", "devmock": "vue-cli-service serve --env VUE_APP_frontendConfigUrl=/template-vue/frontend-config-devmock.json --env VUE_APP_env=devmock", "example": "vue-cli-service build --env VUE_APP_frontendConfigUrl=/template-vue/frontend-config-dev.json --env NODE_ENV=production"}, "dependencies": {"ant-design-vue": "1.7.5", "@liveqing/liveplayer": "2.7.10", "axios": "0.19.2", "core-js": "3.22.5", "mockjs": "1.1.0", "vue": "2.6.14", "vue-router": "3.5.3", "vuex": "3.6.2", "swiper": "5.4.5", "vuex-persistedstate": "3.0.1"}, "devDependencies": {"@babel/core": "7.17.10", "@babel/eslint-parser": "7.17.0", "@vue/cli-plugin-babel": "5.0.4", "@vue/cli-plugin-eslint": "5.0.4", "@vue/cli-plugin-router": "5.0.4", "@vue/cli-plugin-vuex": "5.0.4", "@vue/cli-service": "5.0.4", "@vue/eslint-config-standard": "6.1.0", "babel-plugin-component": "1.1.1", "babel-plugin-import": "1.12.2", "compression-webpack-plugin": "3.1.0", "css-unicode-loader": "1.0.3", "eslint": "7.32.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-vue": "8.7.1", "sass": "1.51.0", "sass-loader": "12.6.0", "vue-template-compiler": "2.6.14"}}
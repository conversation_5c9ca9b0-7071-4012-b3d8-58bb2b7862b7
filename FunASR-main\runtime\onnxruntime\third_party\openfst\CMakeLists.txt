project(openfst)
include(CTest)
find_package(ICU COMPONENTS data i18n io  test tu uc)
if (ICU_FOUND)
  include_directories(${ICU_INCLUDE_DIRS})
  set(LIBS ${LIBS} ${ICU_LIBRARIES})
endif (ICU_FOUND)

find_package(ZLIB)
if (Z<PERSON>IB_FOUND)
  include_directories(${ZLIB_INCLUDE_DIRECTORIES})
  set(ZLIBS ${ZLIB_LIBRARIES})
endif (ZLIB_FOUND)

cmake_minimum_required(VERSION 3.1)
set(CMAKE_MACOSX_RPATH 1)
set(CMAKE_CXX_STANDARD 11)

if (WIN32)
  add_definitions(/bigobj)
  set(WHOLEFST "/WHOLEARCHIVE:fst")
  set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${WHOLEFST}")
  #set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS 1)
  #this must be disabled unless the previous option (CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS) is enabled
  option(BUILD_SHARED_LIBS "Build shared libraries" OFF)
else()
  option(BUILD_SHARED_LIBS "Build shared libraries" ON)
endif (WIN32)

set(SOVERSION "16")
OPTION(BUILD_USE_SOLUTION_FOLDERS "Enable grouping of projects in VS" ON)
SET_PROPERTY(GLOBAL PROPERTY USE_FOLDERS ${BUILD_USE_SOLUTION_FOLDERS})


option(HAVE_BIN          "Build the fst binaries" ON)
option(HAVE_SCRIPT       "Build the fstscript" ON)
option(HAVE_COMPACT      "Build compact" ON)
option(HAVE_COMPRESS "Build compress" OFF)
option(HAVE_CONST   "Build const" ON)
option(HAVE_FAR  "Build far" ON)
option(HAVE_GRM "Build grm" ON)
option(HAVE_PDT "Build pdt" ON)
option(HAVE_MPDT "Build mpdt" ON)
option(HAVE_LINEAR "Build linear" ON)
option(HAVE_LOOKAHEAD "Build lookahead" ON)
option(HAVE_NGRAM "Build ngram" ON)
option(HAVE_PYTHON "Build python" OFF)
option(HAVE_SPECIAL "Build special" ON)

  set(WITH_GFLAGS OFF CACHE BOOL "whether build glog with gflags" FORCE)
  if (WIN32)
    set(HAVE_BIN OFF CACHE BOOL "Build the fst binaries" FORCE)
    set(HAVE_SCRIPT OFF CACHE BOOL "Build the fstscript" FORCE)
  else()
    set(HAVE_BIN ON CACHE BOOL "Build the fst binaries" FORCE)
    set(HAVE_SCRIPT ON CACHE BOOL "Build the fstscript" FORCE)
  endif (WIN32)

  set(HAVE_COMPACT OFF CACHE BOOL "Build compact" FORCE)
  set(HAVE_CONST OFF CACHE BOOL "Build const" FORCE)
  set(HAVE_GRM OFF CACHE BOOL "Build grm" FORCE)
  set(HAVE_FAR OFF CACHE BOOL "Build far" FORCE)
  set(HAVE_PDT OFF CACHE BOOL "Build pdt" FORCE)
  set(HAVE_MPDT OFF CACHE BOOL "Build mpdt" FORCE)
  set(HAVE_LINEAR OFF CACHE BOOL "Build linear" FORCE)
  set(HAVE_LOOKAHEAD OFF CACHE BOOL "Build lookahead" FORCE)
  set(HAVE_NGRAM OFF CACHE BOOL "Build ngram" FORCE)
  set(HAVE_SPECIAL OFF CACHE BOOL "Build special" FORCE)
  
add_subdirectory(src)


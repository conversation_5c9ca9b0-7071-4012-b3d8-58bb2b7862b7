.page-home {
    padding-top: p(62);
    // background: url('../funasr首页3.png') center no-repeat;
    // background-size: 100% 100%;
    background-color: #000000;
    .page-home-header{
        position: fixed;
        top: 0;
        left: 0;
        z-index: 99;
        display: flex;
        width: 100%;
        height: p(62);
        align-items: center;
        border-bottom: solid 1px #202020;
        background-color: #000000;

        .logo-img{
            width: p(110);
            height: p(48);
            margin-left: p(38);
            margin-right: p(20);
        }
        ul{
            display: flex;
            flex: 1;
            height: 100%;
            align-items: center;
            position: relative;
            li{
                color: #ffffff;
                font-size: p(16);
                margin-right: p(20);
                cursor: pointer;
                height: 100%;
                display: flex;
                align-items: center;
                padding: p(0) p(10);
            }
            li:hover{
                background-color: rgb(22, 22, 22);
            }

            .line{
                position: absolute;
                background-color: #1664ff;
                width: p(83);
                height: p(4);
                bottom: p(-1);
                transition: all 0.3s;
            }
        }
        .search-box{
            display: flex;
            align-items: center;
            width: p(440);
            height: p(62);
            background-color: #292929;
            border: solid 1px #464646;
            transition: all 0.3s ease-out;
            img{
                width: p(16);
                height: p(16);
                margin: p(0) p(21);
            }
            .ant-input{
                border: none;
                flex: 1;
                height: p(60);
                background-color: #292929;
                color: #fff;
                font-size: p(16);
                input::-webkit-input-placeholder{
                    color: #cccccc;
                    font-size: p(16);
                    line-height: p(60);
                }
            }
            .ant-input:focus{
                box-shadow: none !important;
            }
            &.search-box:hover{
                background-color: #444444;
                .ant-input{
                    background-color: #444444;

                }
            }
        }
        .language-box{
            color: #ffffff;
            font-size: p(16);
            width: p(193);
            text-align: center;
        }
    }

    .page-home-footer{
        background-color: #1b1c20;
        .gywm{
            display: flex;
            padding: p(0) p(350);
            padding-top: p(38);
            img{
                width: p(200);
	            height: p(85);
                mix-blend-mode: lighten;
                margin-right: p(50);
            }
            ul{
                // flex: 1;
                h3{
                    margin: 0;
                    color: #ffffff;
                    font-size: p(20);
                    margin-bottom: p(12);
                }
                li{
                    color: #999999;
                    font-size: p(16);
                    margin-bottom: p(12);
                    cursor: pointer;
                }
                li:hover{
                    color: #c9c9c9;
                }
            }
            ul:nth-of-type(1){
                margin-right: p(75);
            }
            ul:nth-of-type(2){
                margin-right: p(85);
            }
            ul:nth-of-type(3){
                margin-right: p(85);
            }
            ul:nth-of-type(4){
                margin-right: p(95);
            }
        }

        .line-box{
            margin-top: p(50);
            padding: p(0) p(350);
            div{
                height: 2px;
                background-color: rgba(113, 113, 113, 0.5);
            }
        }
        .ba-box{
            padding: p(26) p(350) p(46);
            ul{
                display: flex;
                justify-content: space-between;
                li{
                    color: #fff;
                    color: #999999;
                    font-size: p(13);
                }
            }
        }
    }
}
